'use client'

import { useEffect, useRef } from 'react'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

// Fix for default markers in Leaflet with Next.js
delete (L.Icon.Default.prototype as any)._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
})

interface MapComponentProps {
  latitude: number
  longitude: number
  accuracy?: number
  onMapReady?: (map: L.Map) => void
  className?: string
}

export default function MapComponent({
  latitude,
  longitude,
  accuracy,
  onMapReady,
  className = "h-96 w-full"
}: MapComponentProps) {
  const mapRef = useRef<L.Map | null>(null)
  const mapContainerRef = useRef<HTMLDivElement>(null)
  const markerRef = useRef<L.Marker | null>(null)
  const accuracyCircleRef = useRef<L.Circle | null>(null)

  console.log('MapComponent rendered with:', { latitude, longitude, accuracy })

  useEffect(() => {
    if (!mapContainerRef.current || mapRef.current) return

    try {
      console.log('Initializing map...')

      // Initialize map
      const map = L.map(mapContainerRef.current).setView([latitude, longitude], 15)

      // Add tile layer
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
      }).addTo(map)

      mapRef.current = map
      onMapReady?.(map)

      console.log('Map initialized successfully')
    } catch (error) {
      console.error('Error initializing map:', error)
    }

    return () => {
      if (mapRef.current) {
        console.log('Cleaning up map...')
        mapRef.current.remove()
        mapRef.current = null
      }
    }
  }, [])

  useEffect(() => {
    if (!mapRef.current) return

    const map = mapRef.current

    // Update marker position
    if (markerRef.current) {
      markerRef.current.setLatLng([latitude, longitude])
    } else {
      markerRef.current = L.marker([latitude, longitude])
        .addTo(map)
        .bindPopup(`Location: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`)
    }

    // Update accuracy circle
    if (accuracy) {
      if (accuracyCircleRef.current) {
        accuracyCircleRef.current.setLatLng([latitude, longitude])
        accuracyCircleRef.current.setRadius(accuracy)
      } else {
        accuracyCircleRef.current = L.circle([latitude, longitude], {
          radius: accuracy,
          color: '#3b82f6',
          fillColor: '#3b82f6',
          fillOpacity: 0.1,
          weight: 2
        }).addTo(map)
      }
    }

    // Center map on new location
    map.setView([latitude, longitude], map.getZoom())
  }, [latitude, longitude, accuracy])

  return (
    <div
      ref={mapContainerRef}
      className={className}
      style={{
        zIndex: 0,
        minHeight: '384px', // Ensure minimum height
        position: 'relative'
      }}
    />
  )
}
