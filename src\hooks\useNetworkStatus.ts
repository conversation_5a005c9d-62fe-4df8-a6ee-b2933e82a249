'use client'

import { useState, useEffect } from 'react'

interface NetworkStatus {
  isOnline: boolean
  isSlowConnection: boolean
  connectionType: string
}

export function useNetworkStatus(): NetworkStatus {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,
    isSlowConnection: false,
    connectionType: 'unknown'
  })

  useEffect(() => {
    const updateNetworkStatus = () => {
      const isOnline = navigator.onLine
      let isSlowConnection = false
      let connectionType = 'unknown'

      // Check connection type if available
      if ('connection' in navigator) {
        const connection = (navigator as any).connection
        connectionType = connection.effectiveType || connection.type || 'unknown'
        
        // Consider 2g and slow-2g as slow connections
        isSlowConnection = ['slow-2g', '2g'].includes(connection.effectiveType)
      }

      setNetworkStatus({
        isOnline,
        isSlowConnection,
        connectionType
      })
    }

    // Initial check
    updateNetworkStatus()

    // Listen for online/offline events
    window.addEventListener('online', updateNetworkStatus)
    window.addEventListener('offline', updateNetworkStatus)

    // Listen for connection changes if supported
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      connection.addEventListener('change', updateNetworkStatus)
    }

    return () => {
      window.removeEventListener('online', updateNetworkStatus)
      window.removeEventListener('offline', updateNetworkStatus)
      
      if ('connection' in navigator) {
        const connection = (navigator as any).connection
        connection.removeEventListener('change', updateNetworkStatus)
      }
    }
  }, [])

  return networkStatus
}
