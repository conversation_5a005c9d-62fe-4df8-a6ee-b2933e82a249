{"name": "@types/leaflet", "version": "1.9.19", "description": "TypeScript definitions for leaflet", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/leaflet", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "alejo90", "url": "https://github.com/alejo90"}, {"name": "<PERSON><PERSON>", "githubUsername": "atd-schubert", "url": "https://github.com/atd-schubert"}, {"name": "<PERSON>", "githubUsername": "m<PERSON>uer", "url": "https://github.com/mcauer"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ronikar"}, {"name": "<PERSON>", "githubUsername": "life777", "url": "https://github.com/life777"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/henry<PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "captain-<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/captain-igloo"}, {"name": "<PERSON>", "githubUsername": "someonewithpc", "url": "https://github.com/someonewithpc"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/leaflet"}, "scripts": {}, "dependencies": {"@types/geojson": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "4308f7c4af017fa08b883b9e6b03ece4889e71bd35d45c32a2fff2a8c26e217c", "typeScriptVersion": "5.1"}