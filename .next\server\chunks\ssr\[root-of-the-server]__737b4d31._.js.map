{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/LiveTracking/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey, {\n  realtime: {\n    params: {\n      eventsPerSecond: 10,\n    },\n  },\n})\n\nexport type Location = {\n  id: string\n  device_id: string\n  latitude: number\n  longitude: number\n  accuracy: number\n  timestamp: string\n  created_at: string\n  updated_at: string\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,iBAAiB;IACjE,UAAU;QACR,QAAQ;YACN,iBAAiB;QACnB;IACF;AACF", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/LiveTracking/src/lib/locationService.ts"], "sourcesContent": ["import { supabase, Location } from './supabase'\nimport { v4 as uuidv4 } from 'uuid'\n\nexport interface LocationData {\n  latitude: number\n  longitude: number\n  accuracy: number\n  timestamp: Date\n}\n\nexport class LocationService {\n  private deviceId: string\n  private watchId: number | null = null\n  private isTracking = false\n  private backgroundInterval: NodeJS.Timeout | null = null\n  private lastLocationTime = 0\n  private visibilityChangeHandler: (() => void) | null = null\n\n  constructor(deviceId?: string) {\n    this.deviceId = deviceId || this.generateDeviceId()\n  }\n\n  private generateDeviceId(): string {\n    let deviceId = localStorage.getItem('device_id')\n    if (!deviceId) {\n      deviceId = uuidv4()\n      localStorage.setItem('device_id', deviceId)\n    }\n    return deviceId\n  }\n\n  async startTracking(onLocationUpdate?: (location: LocationData) => void): Promise<void> {\n    if (this.isTracking) {\n      console.log('Already tracking location')\n      return\n    }\n\n    if (!navigator.geolocation) {\n      throw new Error('Geolocation is not supported by this browser')\n    }\n\n    this.isTracking = true\n\n    // Start primary geolocation tracking\n    await this.startGeolocationWatch(onLocationUpdate)\n\n    // Start background interval for when tab is not active\n    this.startBackgroundTracking(onLocationUpdate)\n\n    // Handle visibility changes to ensure continuous tracking\n    this.setupVisibilityHandling(onLocationUpdate)\n\n    console.log('Location tracking started with background support')\n  }\n\n  private async startGeolocationWatch(onLocationUpdate?: (location: LocationData) => void): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const options: PositionOptions = {\n        enableHighAccuracy: true,\n        timeout: 15000,\n        maximumAge: 0 // Always get fresh location\n      }\n\n      this.watchId = navigator.geolocation.watchPosition(\n        async (position) => {\n          const locationData: LocationData = {\n            latitude: position.coords.latitude,\n            longitude: position.coords.longitude,\n            accuracy: position.coords.accuracy,\n            timestamp: new Date()\n          }\n\n          this.lastLocationTime = Date.now()\n\n          try {\n            await this.saveLocation(locationData)\n            onLocationUpdate?.(locationData)\n            console.log('Location updated via watchPosition')\n            resolve()\n          } catch (error) {\n            console.error('Error saving location:', error)\n          }\n        },\n        (error) => {\n          console.error('Geolocation error:', error)\n          // Don't reject, continue with background tracking\n          resolve()\n        },\n        options\n      )\n    })\n  }\n\n  private startBackgroundTracking(onLocationUpdate?: (location: LocationData) => void): void {\n    // Clear any existing interval\n    if (this.backgroundInterval) {\n      clearInterval(this.backgroundInterval)\n    }\n\n    // Set up aggressive background tracking every 2 seconds\n    this.backgroundInterval = setInterval(async () => {\n      if (!this.isTracking) return\n\n      // Check if we haven't received a location update recently\n      const timeSinceLastUpdate = Date.now() - this.lastLocationTime\n      const shouldForceUpdate = timeSinceLastUpdate > 3000 // 3 seconds\n\n      if (shouldForceUpdate || document.hidden) {\n        console.log('Forcing location update (background/hidden tab)')\n        await this.getCurrentLocationForce(onLocationUpdate)\n      }\n    }, 2000) // Every 2 seconds\n  }\n\n  private async getCurrentLocationForce(onLocationUpdate?: (location: LocationData) => void): Promise<void> {\n    return new Promise((resolve) => {\n      const options: PositionOptions = {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 0\n      }\n\n      navigator.geolocation.getCurrentPosition(\n        async (position) => {\n          const locationData: LocationData = {\n            latitude: position.coords.latitude,\n            longitude: position.coords.longitude,\n            accuracy: position.coords.accuracy,\n            timestamp: new Date()\n          }\n\n          this.lastLocationTime = Date.now()\n\n          try {\n            await this.saveLocation(locationData)\n            onLocationUpdate?.(locationData)\n            console.log('Location updated via getCurrentPosition (background)')\n          } catch (error) {\n            console.error('Error saving background location:', error)\n          }\n          resolve()\n        },\n        (error) => {\n          console.error('Background geolocation error:', error)\n          resolve()\n        },\n        options\n      )\n    })\n  }\n\n  private setupVisibilityHandling(onLocationUpdate?: (location: LocationData) => void): void {\n    // Clean up existing handler\n    if (this.visibilityChangeHandler) {\n      document.removeEventListener('visibilitychange', this.visibilityChangeHandler)\n    }\n\n    this.visibilityChangeHandler = async () => {\n      if (document.hidden) {\n        console.log('Tab became hidden - ensuring background tracking')\n        // Immediately get location when tab becomes hidden\n        await this.getCurrentLocationForce(onLocationUpdate)\n      } else {\n        console.log('Tab became visible - resuming normal tracking')\n        // Restart geolocation watch when tab becomes visible\n        if (this.watchId) {\n          navigator.geolocation.clearWatch(this.watchId)\n        }\n        await this.startGeolocationWatch(onLocationUpdate)\n      }\n    }\n\n    document.addEventListener('visibilitychange', this.visibilityChangeHandler)\n  }\n\n  stopTracking(): void {\n    console.log('Stopping all location tracking')\n\n    // Clear geolocation watch\n    if (this.watchId !== null) {\n      navigator.geolocation.clearWatch(this.watchId)\n      this.watchId = null\n    }\n\n    // Clear background interval\n    if (this.backgroundInterval) {\n      clearInterval(this.backgroundInterval)\n      this.backgroundInterval = null\n    }\n\n    // Remove visibility change handler\n    if (this.visibilityChangeHandler) {\n      document.removeEventListener('visibilitychange', this.visibilityChangeHandler)\n      this.visibilityChangeHandler = null\n    }\n\n    this.isTracking = false\n  }\n\n  private async saveLocation(locationData: LocationData): Promise<void> {\n    const { error } = await supabase\n      .from('locations')\n      .upsert({\n        device_id: this.deviceId,\n        latitude: locationData.latitude,\n        longitude: locationData.longitude,\n        accuracy: locationData.accuracy,\n        timestamp: locationData.timestamp.toISOString()\n      }, {\n        onConflict: 'device_id'\n      })\n\n    if (error) {\n      throw error\n    }\n  }\n\n  async getLatestLocation(deviceId?: string): Promise<Location | null> {\n    const targetDeviceId = deviceId || this.deviceId\n    \n    const { data, error } = await supabase\n      .from('locations')\n      .select('*')\n      .eq('device_id', targetDeviceId)\n      .order('timestamp', { ascending: false })\n      .limit(1)\n      .single()\n\n    if (error && error.code !== 'PGRST116') {\n      throw error\n    }\n\n    return data || null\n  }\n\n  subscribeToLocationUpdates(\n    deviceId: string,\n    onUpdate: (location: Location) => void,\n    onError?: (error: any) => void\n  ) {\n    return supabase\n      .channel(`location-updates-${deviceId}`)\n      .on(\n        'postgres_changes',\n        {\n          event: '*',\n          schema: 'public',\n          table: 'locations',\n          filter: `device_id=eq.${deviceId}`\n        },\n        (payload) => {\n          if (payload.new) {\n            onUpdate(payload.new as Location)\n          }\n        }\n      )\n      .subscribe((status) => {\n        if (status === 'SUBSCRIBED') {\n          console.log('Subscribed to location updates')\n        } else if (status === 'CHANNEL_ERROR') {\n          console.error('Error subscribing to location updates')\n          onError?.(new Error('Subscription failed'))\n        }\n      })\n  }\n\n  getDeviceId(): string {\n    return this.deviceId\n  }\n\n  isCurrentlyTracking(): boolean {\n    return this.isTracking\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AASO,MAAM;IACH,SAAgB;IAChB,UAAyB,KAAI;IAC7B,aAAa,MAAK;IAClB,qBAA4C,KAAI;IAChD,mBAAmB,EAAC;IACpB,0BAA+C,KAAI;IAE3D,YAAY,QAAiB,CAAE;QAC7B,IAAI,CAAC,QAAQ,GAAG,YAAY,IAAI,CAAC,gBAAgB;IACnD;IAEQ,mBAA2B;QACjC,IAAI,WAAW,aAAa,OAAO,CAAC;QACpC,IAAI,CAAC,UAAU;YACb,WAAW,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;YAChB,aAAa,OAAO,CAAC,aAAa;QACpC;QACA,OAAO;IACT;IAEA,MAAM,cAAc,gBAAmD,EAAiB;QACtF,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,UAAU,WAAW,EAAE;YAC1B,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,UAAU,GAAG;QAElB,qCAAqC;QACrC,MAAM,IAAI,CAAC,qBAAqB,CAAC;QAEjC,uDAAuD;QACvD,IAAI,CAAC,uBAAuB,CAAC;QAE7B,0DAA0D;QAC1D,IAAI,CAAC,uBAAuB,CAAC;QAE7B,QAAQ,GAAG,CAAC;IACd;IAEA,MAAc,sBAAsB,gBAAmD,EAAiB;QACtG,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,UAA2B;gBAC/B,oBAAoB;gBACpB,SAAS;gBACT,YAAY,EAAE,4BAA4B;YAC5C;YAEA,IAAI,CAAC,OAAO,GAAG,UAAU,WAAW,CAAC,aAAa,CAChD,OAAO;gBACL,MAAM,eAA6B;oBACjC,UAAU,SAAS,MAAM,CAAC,QAAQ;oBAClC,WAAW,SAAS,MAAM,CAAC,SAAS;oBACpC,UAAU,SAAS,MAAM,CAAC,QAAQ;oBAClC,WAAW,IAAI;gBACjB;gBAEA,IAAI,CAAC,gBAAgB,GAAG,KAAK,GAAG;gBAEhC,IAAI;oBACF,MAAM,IAAI,CAAC,YAAY,CAAC;oBACxB,mBAAmB;oBACnB,QAAQ,GAAG,CAAC;oBACZ;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0BAA0B;gBAC1C;YACF,GACA,CAAC;gBACC,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,kDAAkD;gBAClD;YACF,GACA;QAEJ;IACF;IAEQ,wBAAwB,gBAAmD,EAAQ;QACzF,8BAA8B;QAC9B,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,cAAc,IAAI,CAAC,kBAAkB;QACvC;QAEA,wDAAwD;QACxD,IAAI,CAAC,kBAAkB,GAAG,YAAY;YACpC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAEtB,0DAA0D;YAC1D,MAAM,sBAAsB,KAAK,GAAG,KAAK,IAAI,CAAC,gBAAgB;YAC9D,MAAM,oBAAoB,sBAAsB,KAAK,YAAY;;YAEjE,IAAI,qBAAqB,SAAS,MAAM,EAAE;gBACxC,QAAQ,GAAG,CAAC;gBACZ,MAAM,IAAI,CAAC,uBAAuB,CAAC;YACrC;QACF,GAAG,MAAM,kBAAkB;;IAC7B;IAEA,MAAc,wBAAwB,gBAAmD,EAAiB;QACxG,OAAO,IAAI,QAAQ,CAAC;YAClB,MAAM,UAA2B;gBAC/B,oBAAoB;gBACpB,SAAS;gBACT,YAAY;YACd;YAEA,UAAU,WAAW,CAAC,kBAAkB,CACtC,OAAO;gBACL,MAAM,eAA6B;oBACjC,UAAU,SAAS,MAAM,CAAC,QAAQ;oBAClC,WAAW,SAAS,MAAM,CAAC,SAAS;oBACpC,UAAU,SAAS,MAAM,CAAC,QAAQ;oBAClC,WAAW,IAAI;gBACjB;gBAEA,IAAI,CAAC,gBAAgB,GAAG,KAAK,GAAG;gBAEhC,IAAI;oBACF,MAAM,IAAI,CAAC,YAAY,CAAC;oBACxB,mBAAmB;oBACnB,QAAQ,GAAG,CAAC;gBACd,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,qCAAqC;gBACrD;gBACA;YACF,GACA,CAAC;gBACC,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C;YACF,GACA;QAEJ;IACF;IAEQ,wBAAwB,gBAAmD,EAAQ;QACzF,4BAA4B;QAC5B,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAChC,SAAS,mBAAmB,CAAC,oBAAoB,IAAI,CAAC,uBAAuB;QAC/E;QAEA,IAAI,CAAC,uBAAuB,GAAG;YAC7B,IAAI,SAAS,MAAM,EAAE;gBACnB,QAAQ,GAAG,CAAC;gBACZ,mDAAmD;gBACnD,MAAM,IAAI,CAAC,uBAAuB,CAAC;YACrC,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,qDAAqD;gBACrD,IAAI,IAAI,CAAC,OAAO,EAAE;oBAChB,UAAU,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO;gBAC/C;gBACA,MAAM,IAAI,CAAC,qBAAqB,CAAC;YACnC;QACF;QAEA,SAAS,gBAAgB,CAAC,oBAAoB,IAAI,CAAC,uBAAuB;IAC5E;IAEA,eAAqB;QACnB,QAAQ,GAAG,CAAC;QAEZ,0BAA0B;QAC1B,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM;YACzB,UAAU,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO;YAC7C,IAAI,CAAC,OAAO,GAAG;QACjB;QAEA,4BAA4B;QAC5B,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,cAAc,IAAI,CAAC,kBAAkB;YACrC,IAAI,CAAC,kBAAkB,GAAG;QAC5B;QAEA,mCAAmC;QACnC,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAChC,SAAS,mBAAmB,CAAC,oBAAoB,IAAI,CAAC,uBAAuB;YAC7E,IAAI,CAAC,uBAAuB,GAAG;QACjC;QAEA,IAAI,CAAC,UAAU,GAAG;IACpB;IAEA,MAAc,aAAa,YAA0B,EAAiB;QACpE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,aACL,MAAM,CAAC;YACN,WAAW,IAAI,CAAC,QAAQ;YACxB,UAAU,aAAa,QAAQ;YAC/B,WAAW,aAAa,SAAS;YACjC,UAAU,aAAa,QAAQ;YAC/B,WAAW,aAAa,SAAS,CAAC,WAAW;QAC/C,GAAG;YACD,YAAY;QACd;QAEF,IAAI,OAAO;YACT,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB,QAAiB,EAA4B;QACnE,MAAM,iBAAiB,YAAY,IAAI,CAAC,QAAQ;QAEhD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,gBAChB,KAAK,CAAC,aAAa;YAAE,WAAW;QAAM,GACtC,KAAK,CAAC,GACN,MAAM;QAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY;YACtC,MAAM;QACR;QAEA,OAAO,QAAQ;IACjB;IAEA,2BACE,QAAgB,EAChB,QAAsC,EACtC,OAA8B,EAC9B;QACA,OAAO,sHAAA,CAAA,WAAQ,CACZ,OAAO,CAAC,CAAC,iBAAiB,EAAE,UAAU,EACtC,EAAE,CACD,oBACA;YACE,OAAO;YACP,QAAQ;YACR,OAAO;YACP,QAAQ,CAAC,aAAa,EAAE,UAAU;QACpC,GACA,CAAC;YACC,IAAI,QAAQ,GAAG,EAAE;gBACf,SAAS,QAAQ,GAAG;YACtB;QACF,GAED,SAAS,CAAC,CAAC;YACV,IAAI,WAAW,cAAc;gBAC3B,QAAQ,GAAG,CAAC;YACd,OAAO,IAAI,WAAW,iBAAiB;gBACrC,QAAQ,KAAK,CAAC;gBACd,UAAU,IAAI,MAAM;YACtB;QACF;IACJ;IAEA,cAAsB;QACpB,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA,sBAA+B;QAC7B,OAAO,IAAI,CAAC,UAAU;IACxB;AACF", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/LiveTracking/src/hooks/useNetworkStatus.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\ninterface NetworkStatus {\n  isOnline: boolean\n  isSlowConnection: boolean\n  connectionType: string\n}\n\nexport function useNetworkStatus(): NetworkStatus {\n  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({\n    isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,\n    isSlowConnection: false,\n    connectionType: 'unknown'\n  })\n\n  useEffect(() => {\n    const updateNetworkStatus = () => {\n      const isOnline = navigator.onLine\n      let isSlowConnection = false\n      let connectionType = 'unknown'\n\n      // Check connection type if available\n      if ('connection' in navigator) {\n        const connection = (navigator as any).connection\n        connectionType = connection.effectiveType || connection.type || 'unknown'\n        \n        // Consider 2g and slow-2g as slow connections\n        isSlowConnection = ['slow-2g', '2g'].includes(connection.effectiveType)\n      }\n\n      setNetworkStatus({\n        isOnline,\n        isSlowConnection,\n        connectionType\n      })\n    }\n\n    // Initial check\n    updateNetworkStatus()\n\n    // Listen for online/offline events\n    window.addEventListener('online', updateNetworkStatus)\n    window.addEventListener('offline', updateNetworkStatus)\n\n    // Listen for connection changes if supported\n    if ('connection' in navigator) {\n      const connection = (navigator as any).connection\n      connection.addEventListener('change', updateNetworkStatus)\n    }\n\n    return () => {\n      window.removeEventListener('online', updateNetworkStatus)\n      window.removeEventListener('offline', updateNetworkStatus)\n      \n      if ('connection' in navigator) {\n        const connection = (navigator as any).connection\n        connection.removeEventListener('change', updateNetworkStatus)\n      }\n    }\n  }, [])\n\n  return networkStatus\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAUO,SAAS;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAChE,UAAU,OAAO,cAAc,cAAc,UAAU,MAAM,GAAG;QAChE,kBAAkB;QAClB,gBAAgB;IAClB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB;YAC1B,MAAM,WAAW,UAAU,MAAM;YACjC,IAAI,mBAAmB;YACvB,IAAI,iBAAiB;YAErB,qCAAqC;YACrC,IAAI,gBAAgB,WAAW;gBAC7B,MAAM,aAAa,AAAC,UAAkB,UAAU;gBAChD,iBAAiB,WAAW,aAAa,IAAI,WAAW,IAAI,IAAI;gBAEhE,8CAA8C;gBAC9C,mBAAmB;oBAAC;oBAAW;iBAAK,CAAC,QAAQ,CAAC,WAAW,aAAa;YACxE;YAEA,iBAAiB;gBACf;gBACA;gBACA;YACF;QACF;QAEA,gBAAgB;QAChB;QAEA,mCAAmC;QACnC,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,gBAAgB,CAAC,WAAW;QAEnC,6CAA6C;QAC7C,IAAI,gBAAgB,WAAW;YAC7B,MAAM,aAAa,AAAC,UAAkB,UAAU;YAChD,WAAW,gBAAgB,CAAC,UAAU;QACxC;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,OAAO,mBAAmB,CAAC,WAAW;YAEtC,IAAI,gBAAgB,WAAW;gBAC7B,MAAM,aAAa,AAAC,UAAkB,UAAU;gBAChD,WAAW,mBAAmB,CAAC,UAAU;YAC3C;QACF;IACF,GAAG,EAAE;IAEL,OAAO;AACT", "debugId": null}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/LiveTracking/src/components/ErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\n\ninterface ErrorBoundaryState {\n  hasError: boolean\n  error?: Error\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode\n  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>\n}\n\nclass ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return { hasError: true, error }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('Error caught by boundary:', error, errorInfo)\n  }\n\n  resetError = () => {\n    this.setState({ hasError: false, error: undefined })\n  }\n\n  render() {\n    if (this.state.hasError) {\n      const FallbackComponent = this.props.fallback || DefaultErrorFallback\n      return <FallbackComponent error={this.state.error!} resetError={this.resetError} />\n    }\n\n    return this.props.children\n  }\n}\n\nfunction DefaultErrorFallback({ error, resetError }: { error: Error; resetError: () => void }) {\n  return (\n    <div className=\"min-h-screen bg-red-50 flex items-center justify-center p-4\">\n      <div className=\"bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center\">\n        <div className=\"text-6xl mb-4\">⚠️</div>\n        <h1 className=\"text-2xl font-bold text-red-800 mb-4\">Something went wrong</h1>\n        <p className=\"text-red-600 mb-6\">\n          {error.message || 'An unexpected error occurred'}\n        </p>\n        <div className=\"space-y-3\">\n          <button\n            onClick={resetError}\n            className=\"w-full py-2 bg-red-500 text-white font-semibold rounded-lg hover:bg-red-600 transition-colors\"\n          >\n            Try Again\n          </button>\n          <button\n            onClick={() => window.location.href = '/'}\n            className=\"w-full py-2 bg-gray-500 text-white font-semibold rounded-lg hover:bg-gray-600 transition-colors\"\n          >\n            Go Home\n          </button>\n        </div>\n        <details className=\"mt-6 text-left\">\n          <summary className=\"cursor-pointer text-sm text-gray-500 hover:text-gray-700\">\n            Technical Details\n          </summary>\n          <pre className=\"mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto\">\n            {error.stack}\n          </pre>\n        </details>\n      </div>\n    </div>\n  )\n}\n\nexport default ErrorBoundary\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAcA,MAAM,sBAAsB,qMAAA,CAAA,UAAK,CAAC,SAAS;IACzC,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,6BAA6B,OAAO;IACpD;IAEA,aAAa;QACX,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;QAAU;IACpD,EAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI;YACjD,qBAAO,8OAAC;gBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBAAG,YAAY,IAAI,CAAC,UAAU;;;;;;QACjF;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAEA,SAAS,qBAAqB,EAAE,KAAK,EAAE,UAAU,EAA4C;IAC3F,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BAAgB;;;;;;8BAC/B,8OAAC;oBAAG,WAAU;8BAAuC;;;;;;8BACrD,8OAAC;oBAAE,WAAU;8BACV,MAAM,OAAO,IAAI;;;;;;8BAEpB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;4BACtC,WAAU;sCACX;;;;;;;;;;;;8BAIH,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAQ,WAAU;sCAA2D;;;;;;sCAG9E,8OAAC;4BAAI,WAAU;sCACZ,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;AAMxB;uCAEe", "debugId": null}}, {"offset": {"line": 545, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/LiveTracking/src/app/admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { LocationService, LocationData } from '@/lib/locationService'\nimport { useNetworkStatus } from '@/hooks/useNetworkStatus'\nimport ErrorBoundary from '@/components/ErrorBoundary'\n\nfunction AdminPageContent() {\n  const [isTracking, setIsTracking] = useState(false)\n  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null)\n  const [error, setError] = useState<string | null>(null)\n  const [deviceId, setDeviceId] = useState<string>('')\n  const [locationCount, setLocationCount] = useState(0)\n  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)\n  const [isBackgroundActive, setIsBackgroundActive] = useState(false)\n\n  const locationServiceRef = useRef<LocationService | null>(null)\n  const wakeLockRef = useRef<WakeLockSentinel | null>(null)\n  const networkStatus = useNetworkStatus()\n\n  useEffect(() => {\n    locationServiceRef.current = new LocationService()\n    setDeviceId(locationServiceRef.current.getDeviceId())\n\n    // Listen for background location updates from service worker\n    const handleServiceWorkerMessage = (event: MessageEvent) => {\n      if (event.data?.type === 'BACKGROUND_LOCATION_UPDATE') {\n        const location = event.data.location\n        console.log('Received background location update:', location)\n\n        setCurrentLocation({\n          latitude: location.latitude,\n          longitude: location.longitude,\n          accuracy: location.accuracy,\n          timestamp: new Date(location.timestamp)\n        })\n        setLocationCount(prev => prev + 1)\n        setLastUpdate(new Date())\n      }\n    }\n\n    navigator.serviceWorker?.addEventListener('message', handleServiceWorkerMessage)\n\n    // Handle visibility changes to show background status\n    const handleVisibilityChange = () => {\n      const isHidden = document.hidden\n      setIsBackgroundActive(isHidden && isTracking)\n      console.log(`Tab ${isHidden ? 'hidden' : 'visible'} - Background tracking: ${isHidden && isTracking}`)\n    }\n\n    document.addEventListener('visibilitychange', handleVisibilityChange)\n\n    return () => {\n      if (locationServiceRef.current) {\n        locationServiceRef.current.stopTracking()\n      }\n      releaseWakeLock()\n      navigator.serviceWorker?.removeEventListener('message', handleServiceWorkerMessage)\n      document.removeEventListener('visibilitychange', handleVisibilityChange)\n    }\n  }, [])\n\n  // Update background status when tracking state changes\n  useEffect(() => {\n    setIsBackgroundActive(document.hidden && isTracking)\n  }, [isTracking])\n\n  const requestWakeLock = async () => {\n    try {\n      if ('wakeLock' in navigator) {\n        wakeLockRef.current = await navigator.wakeLock.request('screen')\n        console.log('Wake lock acquired')\n      }\n    } catch (error) {\n      console.error('Wake lock failed:', error)\n    }\n  }\n\n  const releaseWakeLock = () => {\n    if (wakeLockRef.current) {\n      wakeLockRef.current.release()\n      wakeLockRef.current = null\n      console.log('Wake lock released')\n    }\n  }\n\n  const startTracking = async () => {\n    if (!locationServiceRef.current) return\n\n    try {\n      setError(null)\n      setIsTracking(true)\n      \n      await requestWakeLock()\n      \n      await locationServiceRef.current.startTracking((location) => {\n        setCurrentLocation(location)\n        setLocationCount(prev => prev + 1)\n        setLastUpdate(new Date())\n      })\n      \n      console.log('Location tracking started')\n    } catch (error) {\n      setError(error instanceof Error ? error.message : 'Failed to start tracking')\n      setIsTracking(false)\n      releaseWakeLock()\n    }\n  }\n\n  const stopTracking = () => {\n    if (locationServiceRef.current) {\n      locationServiceRef.current.stopTracking()\n      setIsTracking(false)\n      releaseWakeLock()\n      console.log('Location tracking stopped')\n    }\n  }\n\n  const copyDeviceId = () => {\n    navigator.clipboard.writeText(deviceId)\n    alert('Device ID copied to clipboard!')\n  }\n\n  const formatAccuracy = (accuracy: number) => {\n    return accuracy < 1000 ? `${Math.round(accuracy)}m` : `${(accuracy / 1000).toFixed(1)}km`\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4\">\n      <div className=\"max-w-md mx-auto\">\n        <div className=\"bg-white rounded-lg shadow-lg p-6\">\n          <h1 className=\"text-2xl font-bold text-gray-800 mb-6 text-center\">\n            📍 Location Admin\n          </h1>\n\n          {/* Device ID Section */}\n          <div className=\"mb-6 p-4 bg-gray-50 rounded-lg\">\n            <h2 className=\"text-sm font-semibold text-gray-600 mb-2\">Device ID</h2>\n            <div className=\"flex items-center gap-2\">\n              <code className=\"flex-1 text-xs bg-white p-2 rounded border font-mono\">\n                {deviceId}\n              </code>\n              <button\n                onClick={copyDeviceId}\n                className=\"px-3 py-2 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors\"\n              >\n                Copy\n              </button>\n            </div>\n          </div>\n\n          {/* Status Section */}\n          <div className=\"mb-6\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <span className=\"text-lg font-semibold\">Status:</span>\n              <div className=\"flex items-center gap-2\">\n                <span className={`px-3 py-1 rounded-full text-sm font-medium ${\n                  isTracking\n                    ? 'bg-green-100 text-green-800'\n                    : 'bg-gray-100 text-gray-800'\n                }`}>\n                  {isTracking ? '🟢 Active' : '🔴 Inactive'}\n                </span>\n                {isBackgroundActive && (\n                  <span className=\"px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 flex items-center gap-1\">\n                    <span className=\"w-2 h-2 bg-orange-500 rounded-full animate-pulse\"></span>\n                    Background\n                  </span>\n                )}\n              </div>\n            </div>\n\n            {isTracking && (\n              <div className=\"space-y-2 text-sm text-gray-600\">\n                <div>Updates sent: <span className=\"font-semibold\">{locationCount}</span></div>\n                {lastUpdate && (\n                  <div>Last update: <span className=\"font-semibold\">\n                    {lastUpdate.toLocaleTimeString()}\n                  </span></div>\n                )}\n              </div>\n            )}\n          </div>\n\n          {/* Current Location */}\n          {currentLocation && (\n            <div className=\"mb-6 p-4 bg-blue-50 rounded-lg\">\n              <h3 className=\"font-semibold text-blue-800 mb-2\">Current Location</h3>\n              <div className=\"space-y-1 text-sm\">\n                <div>Lat: <span className=\"font-mono\">{currentLocation.latitude.toFixed(6)}</span></div>\n                <div>Lng: <span className=\"font-mono\">{currentLocation.longitude.toFixed(6)}</span></div>\n                <div>Accuracy: <span className=\"font-semibold\">{formatAccuracy(currentLocation.accuracy)}</span></div>\n                <div>Time: <span className=\"font-mono\">{currentLocation.timestamp.toLocaleTimeString()}</span></div>\n              </div>\n            </div>\n          )}\n\n          {/* Error Display */}\n          {error && (\n            <div className=\"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\">\n              <p className=\"text-red-800 text-sm\">{error}</p>\n            </div>\n          )}\n\n          {/* Control Buttons */}\n          <div className=\"space-y-3\">\n            {!isTracking ? (\n              <button\n                onClick={startTracking}\n                className=\"w-full py-3 bg-green-500 text-white font-semibold rounded-lg hover:bg-green-600 transition-colors\"\n              >\n                🚀 Start Broadcasting Location\n              </button>\n            ) : (\n              <button\n                onClick={stopTracking}\n                className=\"w-full py-3 bg-red-500 text-white font-semibold rounded-lg hover:bg-red-600 transition-colors\"\n              >\n                ⏹️ Stop Broadcasting\n              </button>\n            )}\n          </div>\n\n          {/* Instructions */}\n          <div className=\"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n            <h3 className=\"font-semibold text-yellow-800 mb-2\">📋 Instructions</h3>\n            <ol className=\"text-sm text-yellow-700 space-y-1 list-decimal list-inside\">\n              <li>Copy your Device ID above</li>\n              <li>Share it with the tracking device</li>\n              <li>Click \"Start Broadcasting\" to begin</li>\n              <li>Location continues broadcasting even when tab is hidden</li>\n              <li>Background mode indicator shows when tab is not active</li>\n            </ol>\n          </div>\n\n          {/* Network Status */}\n          {!networkStatus.isOnline && (\n            <div className=\"mt-4 p-3 bg-red-100 border border-red-300 rounded-lg\">\n              <p className=\"text-red-800 text-sm font-medium\">📡 No internet connection</p>\n              <p className=\"text-red-600 text-xs\">Location updates will resume when connection is restored</p>\n            </div>\n          )}\n\n          {networkStatus.isSlowConnection && networkStatus.isOnline && (\n            <div className=\"mt-4 p-3 bg-yellow-100 border border-yellow-300 rounded-lg\">\n              <p className=\"text-yellow-800 text-sm font-medium\">🐌 Slow connection detected</p>\n              <p className=\"text-yellow-600 text-xs\">Location updates may be delayed</p>\n            </div>\n          )}\n\n          {/* Technical Info */}\n          <div className=\"mt-4 text-xs text-gray-500 text-center space-y-1\">\n            <p>⚡ Wake lock: {wakeLockRef.current ? 'Active' : 'Inactive'}</p>\n            <p>🔄 Updates every ~1 second when active</p>\n            <p>📡 Network: {networkStatus.isOnline ? 'Online' : 'Offline'} ({networkStatus.connectionType})</p>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n\nexport default function AdminPage() {\n  return (\n    <ErrorBoundary>\n      <AdminPageContent />\n    </ErrorBoundary>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,SAAS;IACP,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAC5E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC1D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0B;IAC1D,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA2B;IACpD,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mBAAmB,OAAO,GAAG,IAAI,6HAAA,CAAA,kBAAe;QAChD,YAAY,mBAAmB,OAAO,CAAC,WAAW;QAElD,6DAA6D;QAC7D,MAAM,6BAA6B,CAAC;YAClC,IAAI,MAAM,IAAI,EAAE,SAAS,8BAA8B;gBACrD,MAAM,WAAW,MAAM,IAAI,CAAC,QAAQ;gBACpC,QAAQ,GAAG,CAAC,wCAAwC;gBAEpD,mBAAmB;oBACjB,UAAU,SAAS,QAAQ;oBAC3B,WAAW,SAAS,SAAS;oBAC7B,UAAU,SAAS,QAAQ;oBAC3B,WAAW,IAAI,KAAK,SAAS,SAAS;gBACxC;gBACA,iBAAiB,CAAA,OAAQ,OAAO;gBAChC,cAAc,IAAI;YACpB;QACF;QAEA,UAAU,aAAa,EAAE,iBAAiB,WAAW;QAErD,sDAAsD;QACtD,MAAM,yBAAyB;YAC7B,MAAM,WAAW,SAAS,MAAM;YAChC,sBAAsB,YAAY;YAClC,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,WAAW,WAAW,UAAU,wBAAwB,EAAE,YAAY,YAAY;QACvG;QAEA,SAAS,gBAAgB,CAAC,oBAAoB;QAE9C,OAAO;YACL,IAAI,mBAAmB,OAAO,EAAE;gBAC9B,mBAAmB,OAAO,CAAC,YAAY;YACzC;YACA;YACA,UAAU,aAAa,EAAE,oBAAoB,WAAW;YACxD,SAAS,mBAAmB,CAAC,oBAAoB;QACnD;IACF,GAAG,EAAE;IAEL,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sBAAsB,SAAS,MAAM,IAAI;IAC3C,GAAG;QAAC;KAAW;IAEf,MAAM,kBAAkB;QACtB,IAAI;YACF,IAAI,cAAc,WAAW;gBAC3B,YAAY,OAAO,GAAG,MAAM,UAAU,QAAQ,CAAC,OAAO,CAAC;gBACvD,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;QACrC;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,YAAY,OAAO,EAAE;YACvB,YAAY,OAAO,CAAC,OAAO;YAC3B,YAAY,OAAO,GAAG;YACtB,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAEjC,IAAI;YACF,SAAS;YACT,cAAc;YAEd,MAAM;YAEN,MAAM,mBAAmB,OAAO,CAAC,aAAa,CAAC,CAAC;gBAC9C,mBAAmB;gBACnB,iBAAiB,CAAA,OAAQ,OAAO;gBAChC,cAAc,IAAI;YACpB;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,cAAc;YACd;QACF;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,mBAAmB,OAAO,EAAE;YAC9B,mBAAmB,OAAO,CAAC,YAAY;YACvC,cAAc;YACd;YACA,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,MAAM,eAAe;QACnB,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,MAAM;IACR;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,WAAW,OAAO,GAAG,KAAK,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,GAAG,CAAC,WAAW,IAAI,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;IAC3F;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoD;;;;;;kCAKlE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDACb;;;;;;kDAEH,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAW,CAAC,2CAA2C,EAC3D,aACI,gCACA,6BACJ;0DACC,aAAa,cAAc;;;;;;4CAE7B,oCACC,8OAAC;gDAAK,WAAU;;kEACd,8OAAC;wDAAK,WAAU;;;;;;oDAA0D;;;;;;;;;;;;;;;;;;;4BAOjF,4BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAI;0DAAc,8OAAC;gDAAK,WAAU;0DAAiB;;;;;;;;;;;;oCACnD,4BACC,8OAAC;;4CAAI;0DAAa,8OAAC;gDAAK,WAAU;0DAC/B,WAAW,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;oBAQvC,iCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAI;0DAAK,8OAAC;gDAAK,WAAU;0DAAa,gBAAgB,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;kDACxE,8OAAC;;4CAAI;0DAAK,8OAAC;gDAAK,WAAU;0DAAa,gBAAgB,SAAS,CAAC,OAAO,CAAC;;;;;;;;;;;;kDACzE,8OAAC;;4CAAI;0DAAU,8OAAC;gDAAK,WAAU;0DAAiB,eAAe,gBAAgB,QAAQ;;;;;;;;;;;;kDACvF,8OAAC;;4CAAI;0DAAM,8OAAC;gDAAK,WAAU;0DAAa,gBAAgB,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;oBAMzF,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;kCAKzC,8OAAC;wBAAI,WAAU;kCACZ,CAAC,2BACA,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;iDAID,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;oBAKP,CAAC,cAAc,QAAQ,kBACtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,8OAAC;gCAAE,WAAU;0CAAuB;;;;;;;;;;;;oBAIvC,cAAc,gBAAgB,IAAI,cAAc,QAAQ,kBACvD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAsC;;;;;;0CACnD,8OAAC;gCAAE,WAAU;0CAA0B;;;;;;;;;;;;kCAK3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAE;oCAAc,YAAY,OAAO,GAAG,WAAW;;;;;;;0CAClD,8OAAC;0CAAE;;;;;;0CACH,8OAAC;;oCAAE;oCAAa,cAAc,QAAQ,GAAG,WAAW;oCAAU;oCAAG,cAAc,cAAc;oCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1G;AAEe,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,UAAa;kBACZ,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}