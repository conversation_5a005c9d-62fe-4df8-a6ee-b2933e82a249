// Location tracking Web Worker
// This worker runs in a separate thread and can continue tracking even when main thread is throttled

let trackingInterval = null;
let isTracking = false;
let deviceId = null;
let supabaseUrl = null;
let supabaseKey = null;

// Listen for messages from main thread
self.addEventListener('message', async (event) => {
  const { type, data } = event.data;
  
  console.log('Location worker received message:', type);
  
  switch (type) {
    case 'START_TRACKING':
      startTracking(data);
      break;
    case 'STOP_TRACKING':
      stopTracking();
      break;
    case 'UPDATE_CONFIG':
      updateConfig(data);
      break;
  }
});

function updateConfig(config) {
  deviceId = config.deviceId;
  supabaseUrl = config.supabaseUrl;
  supabaseKey = config.supabaseKey;
  console.log('Location worker config updated');
}

function startTracking(config) {
  if (isTracking) {
    console.log('Location worker already tracking');
    return;
  }
  
  updateConfig(config);
  isTracking = true;
  
  console.log('Starting location tracking in worker');
  
  // Start aggressive location tracking every 3 seconds
  trackingInterval = setInterval(async () => {
    try {
      await getCurrentLocationAndSave();
    } catch (error) {
      console.error('Worker location tracking error:', error);
      
      // Notify main thread of error
      self.postMessage({
        type: 'LOCATION_ERROR',
        error: error.message
      });
    }
  }, 3000); // Every 3 seconds
  
  // Also get initial location immediately
  getCurrentLocationAndSave();
}

function stopTracking() {
  console.log('Stopping location tracking in worker');
  
  if (trackingInterval) {
    clearInterval(trackingInterval);
    trackingInterval = null;
  }
  
  isTracking = false;
  
  self.postMessage({
    type: 'TRACKING_STOPPED'
  });
}

async function getCurrentLocationAndSave() {
  return new Promise((resolve, reject) => {
    // Check if geolocation is available
    if (!('geolocation' in navigator)) {
      reject(new Error('Geolocation not available in worker'));
      return;
    }
    
    const options = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 0
    };
    
    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const locationData = {
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: new Date().toISOString()
        };
        
        console.log('Worker got location:', locationData);
        
        try {
          // Save to Supabase
          await saveLocationToSupabase(locationData);
          
          // Notify main thread
          self.postMessage({
            type: 'LOCATION_UPDATE',
            location: locationData
          });
          
          resolve(locationData);
        } catch (error) {
          console.error('Worker failed to save location:', error);
          reject(error);
        }
      },
      (error) => {
        console.error('Worker geolocation error:', error);
        reject(error);
      },
      options
    );
  });
}

async function saveLocationToSupabase(locationData) {
  if (!supabaseUrl || !supabaseKey || !deviceId) {
    throw new Error('Supabase configuration missing');
  }
  
  const url = `${supabaseUrl}/rest/v1/locations`;
  
  const payload = {
    device_id: deviceId,
    latitude: locationData.latitude,
    longitude: locationData.longitude,
    accuracy: locationData.accuracy,
    timestamp: locationData.timestamp
  };
  
  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'apikey': supabaseKey,
      'Authorization': `Bearer ${supabaseKey}`,
      'Prefer': 'resolution=merge-duplicates'
    },
    body: JSON.stringify(payload)
  });
  
  if (!response.ok) {
    throw new Error(`Supabase request failed: ${response.status} ${response.statusText}`);
  }
  
  console.log('Worker saved location to Supabase');
}

// Handle worker errors
self.addEventListener('error', (error) => {
  console.error('Location worker error:', error);
  self.postMessage({
    type: 'WORKER_ERROR',
    error: error.message
  });
});

console.log('Location worker initialized');
