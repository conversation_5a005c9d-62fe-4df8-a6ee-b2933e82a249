{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/LiveTracking/src/components/MapComponent.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef } from 'react'\nimport L from 'leaflet'\nimport 'leaflet/dist/leaflet.css'\n\n// Fix for default markers in Leaflet with Next.js\ndelete (L.Icon.Default.prototype as any)._getIconUrl\nL.Icon.Default.mergeOptions({\n  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',\n})\n\ninterface MapComponentProps {\n  latitude: number\n  longitude: number\n  accuracy?: number\n  onMapReady?: (map: L.Map) => void\n  className?: string\n}\n\nexport default function MapComponent({\n  latitude,\n  longitude,\n  accuracy,\n  onMapReady,\n  className = \"h-96 w-full\"\n}: MapComponentProps) {\n  const mapRef = useRef<L.Map | null>(null)\n  const mapContainerRef = useRef<HTMLDivElement>(null)\n  const markerRef = useRef<L.Marker | null>(null)\n  const accuracyCircleRef = useRef<L.Circle | null>(null)\n\n  console.log('MapComponent rendered with:', { latitude, longitude, accuracy })\n\n  useEffect(() => {\n    if (!mapContainerRef.current || mapRef.current) return\n\n    try {\n      console.log('Initializing map...')\n\n      // Initialize map\n      const map = L.map(mapContainerRef.current).setView([latitude, longitude], 15)\n\n      // Add tile layer\n      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {\n        attribution: '© OpenStreetMap contributors'\n      }).addTo(map)\n\n      mapRef.current = map\n      onMapReady?.(map)\n\n      console.log('Map initialized successfully')\n    } catch (error) {\n      console.error('Error initializing map:', error)\n    }\n\n    return () => {\n      if (mapRef.current) {\n        console.log('Cleaning up map...')\n        mapRef.current.remove()\n        mapRef.current = null\n      }\n    }\n  }, [])\n\n  useEffect(() => {\n    if (!mapRef.current) return\n\n    const map = mapRef.current\n\n    // Update marker position\n    if (markerRef.current) {\n      markerRef.current.setLatLng([latitude, longitude])\n    } else {\n      markerRef.current = L.marker([latitude, longitude])\n        .addTo(map)\n        .bindPopup(`Location: ${latitude.toFixed(6)}, ${longitude.toFixed(6)}`)\n    }\n\n    // Update accuracy circle\n    if (accuracy) {\n      if (accuracyCircleRef.current) {\n        accuracyCircleRef.current.setLatLng([latitude, longitude])\n        accuracyCircleRef.current.setRadius(accuracy)\n      } else {\n        accuracyCircleRef.current = L.circle([latitude, longitude], {\n          radius: accuracy,\n          color: '#3b82f6',\n          fillColor: '#3b82f6',\n          fillOpacity: 0.1,\n          weight: 2\n        }).addTo(map)\n      }\n    }\n\n    // Center map on new location\n    map.setView([latitude, longitude], map.getZoom())\n  }, [latitude, longitude, accuracy])\n\n  return (\n    <div\n      ref={mapContainerRef}\n      className={className}\n      style={{\n        zIndex: 0,\n        minHeight: '384px', // Ensure minimum height\n        position: 'relative'\n      }}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;;AAMA,kDAAkD;AAClD,OAAO,AAAC,oJAAA,CAAA,UAAC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAS,WAAW;AACpD,oJAAA,CAAA,UAAC,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;IAC1B,eAAe;IACf,SAAS;IACT,WAAW;AACb;AAUe,SAAS,aAAa,EACnC,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,UAAU,EACV,YAAY,aAAa,EACP;;IAClB,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAgB;IACpC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC/C,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAmB;IAC1C,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAmB;IAElD,QAAQ,GAAG,CAAC,+BAA+B;QAAE;QAAU;QAAW;IAAS;IAE3E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,gBAAgB,OAAO,IAAI,OAAO,OAAO,EAAE;YAEhD,IAAI;gBACF,QAAQ,GAAG,CAAC;gBAEZ,iBAAiB;gBACjB,MAAM,MAAM,oJAAA,CAAA,UAAC,CAAC,GAAG,CAAC,gBAAgB,OAAO,EAAE,OAAO,CAAC;oBAAC;oBAAU;iBAAU,EAAE;gBAE1E,iBAAiB;gBACjB,oJAAA,CAAA,UAAC,CAAC,SAAS,CAAC,sDAAsD;oBAChE,aAAa;gBACf,GAAG,KAAK,CAAC;gBAET,OAAO,OAAO,GAAG;gBACjB,aAAa;gBAEb,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;YAEA;0CAAO;oBACL,IAAI,OAAO,OAAO,EAAE;wBAClB,QAAQ,GAAG,CAAC;wBACZ,OAAO,OAAO,CAAC,MAAM;wBACrB,OAAO,OAAO,GAAG;oBACnB;gBACF;;QACF;iCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,OAAO,OAAO,EAAE;YAErB,MAAM,MAAM,OAAO,OAAO;YAE1B,yBAAyB;YACzB,IAAI,UAAU,OAAO,EAAE;gBACrB,UAAU,OAAO,CAAC,SAAS,CAAC;oBAAC;oBAAU;iBAAU;YACnD,OAAO;gBACL,UAAU,OAAO,GAAG,oJAAA,CAAA,UAAC,CAAC,MAAM,CAAC;oBAAC;oBAAU;iBAAU,EAC/C,KAAK,CAAC,KACN,SAAS,CAAC,CAAC,UAAU,EAAE,SAAS,OAAO,CAAC,GAAG,EAAE,EAAE,UAAU,OAAO,CAAC,IAAI;YAC1E;YAEA,yBAAyB;YACzB,IAAI,UAAU;gBACZ,IAAI,kBAAkB,OAAO,EAAE;oBAC7B,kBAAkB,OAAO,CAAC,SAAS,CAAC;wBAAC;wBAAU;qBAAU;oBACzD,kBAAkB,OAAO,CAAC,SAAS,CAAC;gBACtC,OAAO;oBACL,kBAAkB,OAAO,GAAG,oJAAA,CAAA,UAAC,CAAC,MAAM,CAAC;wBAAC;wBAAU;qBAAU,EAAE;wBAC1D,QAAQ;wBACR,OAAO;wBACP,WAAW;wBACX,aAAa;wBACb,QAAQ;oBACV,GAAG,KAAK,CAAC;gBACX;YACF;YAEA,6BAA6B;YAC7B,IAAI,OAAO,CAAC;gBAAC;gBAAU;aAAU,EAAE,IAAI,OAAO;QAChD;iCAAG;QAAC;QAAU;QAAW;KAAS;IAElC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW;QACX,OAAO;YACL,QAAQ;YACR,WAAW;YACX,UAAU;QACZ;;;;;;AAGN;GA1FwB;KAAA", "debugId": null}}]}