import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-100">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto text-center">
          {/* Header */}
          <div className="mb-12">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-800 mb-4">
              📍 Live Tracking
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Real-time location sharing between devices using GPS and Supabase
            </p>
          </div>

          {/* Feature Cards */}
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            {/* Admin Card */}
            <div className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
              <div className="text-4xl mb-4">📱</div>
              <h2 className="text-2xl font-bold text-gray-800 mb-4">Admin Page</h2>
              <p className="text-gray-600 mb-6">
                Broadcast your device's GPS location in real-time. Perfect for the device that needs to share its location.
              </p>
              <ul className="text-sm text-gray-500 mb-6 space-y-2">
                <li>✅ Continuous GPS tracking</li>
                <li>✅ Real-time broadcasting</li>
                <li>✅ Wake lock support</li>
                <li>✅ High accuracy positioning</li>
              </ul>
              <Link
                href="/admin"
                className="inline-block w-full py-3 bg-green-500 text-white font-semibold rounded-lg hover:bg-green-600 transition-colors"
              >
                🚀 Start Broadcasting
              </Link>
            </div>

            {/* Tracking Card */}
            <div className="bg-white rounded-xl shadow-lg p-8 hover:shadow-xl transition-shadow">
              <div className="text-4xl mb-4">🗺️</div>
              <h2 className="text-2xl font-bold text-gray-800 mb-4">Live Tracking</h2>
              <p className="text-gray-600 mb-6">
                View real-time location updates on an interactive map. Perfect for monitoring a remote device's location.
              </p>
              <ul className="text-sm text-gray-500 mb-6 space-y-2">
                <li>✅ Interactive map display</li>
                <li>✅ Real-time updates</li>
                <li>✅ Accuracy indicators</li>
                <li>✅ Connection status</li>
              </ul>
              <Link
                href="/track"
                className="inline-block w-full py-3 bg-blue-500 text-white font-semibold rounded-lg hover:bg-blue-600 transition-colors"
              >
                🔍 Track Location
              </Link>
            </div>
          </div>

          {/* How it Works */}
          <div className="bg-white rounded-xl shadow-lg p-8 mb-12">
            <h2 className="text-2xl font-bold text-gray-800 mb-6">🔄 How It Works</h2>
            <div className="grid md:grid-cols-3 gap-6 text-left">
              <div className="text-center">
                <div className="text-3xl mb-3">1️⃣</div>
                <h3 className="font-semibold mb-2">Setup Admin</h3>
                <p className="text-sm text-gray-600">
                  Open the Admin page on Phone A and start broadcasting location
                </p>
              </div>
              <div className="text-center">
                <div className="text-3xl mb-3">2️⃣</div>
                <h3 className="font-semibold mb-2">Copy Device ID</h3>
                <p className="text-sm text-gray-600">
                  Copy the unique Device ID from the Admin page
                </p>
              </div>
              <div className="text-center">
                <div className="text-3xl mb-3">3️⃣</div>
                <h3 className="font-semibold mb-2">Track Live</h3>
                <p className="text-sm text-gray-600">
                  Enter the Device ID on Phone B to view real-time location
                </p>
              </div>
            </div>
          </div>

          {/* Technical Info */}
          <div className="bg-gray-50 rounded-xl p-6">
            <h3 className="font-semibold text-gray-800 mb-3">⚡ Technical Features</h3>
            <div className="grid md:grid-cols-2 gap-4 text-sm text-gray-600">
              <div>• Real-time communication via Supabase</div>
              <div>• High-accuracy GPS positioning</div>
              <div>• Interactive Leaflet maps</div>
              <div>• Mobile-optimized interface</div>
              <div>• Wake lock for continuous tracking</div>
              <div>• Automatic reconnection handling</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
