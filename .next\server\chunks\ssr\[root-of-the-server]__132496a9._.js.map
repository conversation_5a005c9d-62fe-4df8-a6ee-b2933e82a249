{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/LiveTracking/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey, {\n  realtime: {\n    params: {\n      eventsPerSecond: 10,\n    },\n  },\n})\n\nexport type Location = {\n  id: string\n  device_id: string\n  latitude: number\n  longitude: number\n  accuracy: number\n  timestamp: string\n  created_at: string\n  updated_at: string\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,iBAAiB;IACjE,UAAU;QACR,QAAQ;YACN,iBAAiB;QACnB;IACF;AACF", "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/LiveTracking/src/lib/locationService.ts"], "sourcesContent": ["import { supabase, Location } from './supabase'\nimport { v4 as uuidv4 } from 'uuid'\n\nexport interface LocationData {\n  latitude: number\n  longitude: number\n  accuracy: number\n  timestamp: Date\n}\n\nexport class LocationService {\n  private deviceId: string\n  private watchId: number | null = null\n  private isTracking = false\n\n  constructor(deviceId?: string) {\n    this.deviceId = deviceId || this.generateDeviceId()\n  }\n\n  private generateDeviceId(): string {\n    let deviceId = localStorage.getItem('device_id')\n    if (!deviceId) {\n      deviceId = uuidv4()\n      localStorage.setItem('device_id', deviceId)\n    }\n    return deviceId\n  }\n\n  async startTracking(onLocationUpdate?: (location: LocationData) => void): Promise<void> {\n    if (this.isTracking) {\n      console.log('Already tracking location')\n      return\n    }\n\n    if (!navigator.geolocation) {\n      throw new Error('Geolocation is not supported by this browser')\n    }\n\n    return new Promise((resolve, reject) => {\n      const options: PositionOptions = {\n        enableHighAccuracy: true,\n        timeout: 10000,\n        maximumAge: 1000\n      }\n\n      this.watchId = navigator.geolocation.watchPosition(\n        async (position) => {\n          const locationData: LocationData = {\n            latitude: position.coords.latitude,\n            longitude: position.coords.longitude,\n            accuracy: position.coords.accuracy,\n            timestamp: new Date()\n          }\n\n          try {\n            await this.saveLocation(locationData)\n            onLocationUpdate?.(locationData)\n            \n            if (!this.isTracking) {\n              this.isTracking = true\n              resolve()\n            }\n          } catch (error) {\n            console.error('Error saving location:', error)\n          }\n        },\n        (error) => {\n          console.error('Geolocation error:', error)\n          if (!this.isTracking) {\n            reject(error)\n          }\n        },\n        options\n      )\n    })\n  }\n\n  stopTracking(): void {\n    if (this.watchId !== null) {\n      navigator.geolocation.clearWatch(this.watchId)\n      this.watchId = null\n    }\n    this.isTracking = false\n  }\n\n  private async saveLocation(locationData: LocationData): Promise<void> {\n    const { error } = await supabase\n      .from('locations')\n      .upsert({\n        device_id: this.deviceId,\n        latitude: locationData.latitude,\n        longitude: locationData.longitude,\n        accuracy: locationData.accuracy,\n        timestamp: locationData.timestamp.toISOString()\n      }, {\n        onConflict: 'device_id'\n      })\n\n    if (error) {\n      throw error\n    }\n  }\n\n  async getLatestLocation(deviceId?: string): Promise<Location | null> {\n    const targetDeviceId = deviceId || this.deviceId\n    \n    const { data, error } = await supabase\n      .from('locations')\n      .select('*')\n      .eq('device_id', targetDeviceId)\n      .order('timestamp', { ascending: false })\n      .limit(1)\n      .single()\n\n    if (error && error.code !== 'PGRST116') {\n      throw error\n    }\n\n    return data || null\n  }\n\n  subscribeToLocationUpdates(\n    deviceId: string,\n    onUpdate: (location: Location) => void,\n    onError?: (error: any) => void\n  ) {\n    return supabase\n      .channel(`location-updates-${deviceId}`)\n      .on(\n        'postgres_changes',\n        {\n          event: '*',\n          schema: 'public',\n          table: 'locations',\n          filter: `device_id=eq.${deviceId}`\n        },\n        (payload) => {\n          if (payload.new) {\n            onUpdate(payload.new as Location)\n          }\n        }\n      )\n      .subscribe((status) => {\n        if (status === 'SUBSCRIBED') {\n          console.log('Subscribed to location updates')\n        } else if (status === 'CHANNEL_ERROR') {\n          console.error('Error subscribing to location updates')\n          onError?.(new Error('Subscription failed'))\n        }\n      })\n  }\n\n  getDeviceId(): string {\n    return this.deviceId\n  }\n\n  isCurrentlyTracking(): boolean {\n    return this.isTracking\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AASO,MAAM;IACH,SAAgB;IAChB,UAAyB,KAAI;IAC7B,aAAa,MAAK;IAE1B,YAAY,QAAiB,CAAE;QAC7B,IAAI,CAAC,QAAQ,GAAG,YAAY,IAAI,CAAC,gBAAgB;IACnD;IAEQ,mBAA2B;QACjC,IAAI,WAAW,aAAa,OAAO,CAAC;QACpC,IAAI,CAAC,UAAU;YACb,WAAW,CAAA,GAAA,0KAAA,CAAA,KAAM,AAAD;YAChB,aAAa,OAAO,CAAC,aAAa;QACpC;QACA,OAAO;IACT;IAEA,MAAM,cAAc,gBAAmD,EAAiB;QACtF,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,UAAU,WAAW,EAAE;YAC1B,MAAM,IAAI,MAAM;QAClB;QAEA,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,UAA2B;gBAC/B,oBAAoB;gBACpB,SAAS;gBACT,YAAY;YACd;YAEA,IAAI,CAAC,OAAO,GAAG,UAAU,WAAW,CAAC,aAAa,CAChD,OAAO;gBACL,MAAM,eAA6B;oBACjC,UAAU,SAAS,MAAM,CAAC,QAAQ;oBAClC,WAAW,SAAS,MAAM,CAAC,SAAS;oBACpC,UAAU,SAAS,MAAM,CAAC,QAAQ;oBAClC,WAAW,IAAI;gBACjB;gBAEA,IAAI;oBACF,MAAM,IAAI,CAAC,YAAY,CAAC;oBACxB,mBAAmB;oBAEnB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;wBACpB,IAAI,CAAC,UAAU,GAAG;wBAClB;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0BAA0B;gBAC1C;YACF,GACA,CAAC;gBACC,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;oBACpB,OAAO;gBACT;YACF,GACA;QAEJ;IACF;IAEA,eAAqB;QACnB,IAAI,IAAI,CAAC,OAAO,KAAK,MAAM;YACzB,UAAU,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO;YAC7C,IAAI,CAAC,OAAO,GAAG;QACjB;QACA,IAAI,CAAC,UAAU,GAAG;IACpB;IAEA,MAAc,aAAa,YAA0B,EAAiB;QACpE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,aACL,MAAM,CAAC;YACN,WAAW,IAAI,CAAC,QAAQ;YACxB,UAAU,aAAa,QAAQ;YAC/B,WAAW,aAAa,SAAS;YACjC,UAAU,aAAa,QAAQ;YAC/B,WAAW,aAAa,SAAS,CAAC,WAAW;QAC/C,GAAG;YACD,YAAY;QACd;QAEF,IAAI,OAAO;YACT,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB,QAAiB,EAA4B;QACnE,MAAM,iBAAiB,YAAY,IAAI,CAAC,QAAQ;QAEhD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,gBAChB,KAAK,CAAC,aAAa;YAAE,WAAW;QAAM,GACtC,KAAK,CAAC,GACN,MAAM;QAET,IAAI,SAAS,MAAM,IAAI,KAAK,YAAY;YACtC,MAAM;QACR;QAEA,OAAO,QAAQ;IACjB;IAEA,2BACE,QAAgB,EAChB,QAAsC,EACtC,OAA8B,EAC9B;QACA,OAAO,sHAAA,CAAA,WAAQ,CACZ,OAAO,CAAC,CAAC,iBAAiB,EAAE,UAAU,EACtC,EAAE,CACD,oBACA;YACE,OAAO;YACP,QAAQ;YACR,OAAO;YACP,QAAQ,CAAC,aAAa,EAAE,UAAU;QACpC,GACA,CAAC;YACC,IAAI,QAAQ,GAAG,EAAE;gBACf,SAAS,QAAQ,GAAG;YACtB;QACF,GAED,SAAS,CAAC,CAAC;YACV,IAAI,WAAW,cAAc;gBAC3B,QAAQ,GAAG,CAAC;YACd,OAAO,IAAI,WAAW,iBAAiB;gBACrC,QAAQ,KAAK,CAAC;gBACd,UAAU,IAAI,MAAM;YACtB;QACF;IACJ;IAEA,cAAsB;QACpB,OAAO,IAAI,CAAC,QAAQ;IACtB;IAEA,sBAA+B;QAC7B,OAAO,IAAI,CAAC,UAAU;IACxB;AACF", "debugId": null}}, {"offset": {"line": 247, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/LiveTracking/src/hooks/useNetworkStatus.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\ninterface NetworkStatus {\n  isOnline: boolean\n  isSlowConnection: boolean\n  connectionType: string\n}\n\nexport function useNetworkStatus(): NetworkStatus {\n  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({\n    isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,\n    isSlowConnection: false,\n    connectionType: 'unknown'\n  })\n\n  useEffect(() => {\n    const updateNetworkStatus = () => {\n      const isOnline = navigator.onLine\n      let isSlowConnection = false\n      let connectionType = 'unknown'\n\n      // Check connection type if available\n      if ('connection' in navigator) {\n        const connection = (navigator as any).connection\n        connectionType = connection.effectiveType || connection.type || 'unknown'\n        \n        // Consider 2g and slow-2g as slow connections\n        isSlowConnection = ['slow-2g', '2g'].includes(connection.effectiveType)\n      }\n\n      setNetworkStatus({\n        isOnline,\n        isSlowConnection,\n        connectionType\n      })\n    }\n\n    // Initial check\n    updateNetworkStatus()\n\n    // Listen for online/offline events\n    window.addEventListener('online', updateNetworkStatus)\n    window.addEventListener('offline', updateNetworkStatus)\n\n    // Listen for connection changes if supported\n    if ('connection' in navigator) {\n      const connection = (navigator as any).connection\n      connection.addEventListener('change', updateNetworkStatus)\n    }\n\n    return () => {\n      window.removeEventListener('online', updateNetworkStatus)\n      window.removeEventListener('offline', updateNetworkStatus)\n      \n      if ('connection' in navigator) {\n        const connection = (navigator as any).connection\n        connection.removeEventListener('change', updateNetworkStatus)\n      }\n    }\n  }, [])\n\n  return networkStatus\n}\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAUO,SAAS;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAChE,UAAU,OAAO,cAAc,cAAc,UAAU,MAAM,GAAG;QAChE,kBAAkB;QAClB,gBAAgB;IAClB;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB;YAC1B,MAAM,WAAW,UAAU,MAAM;YACjC,IAAI,mBAAmB;YACvB,IAAI,iBAAiB;YAErB,qCAAqC;YACrC,IAAI,gBAAgB,WAAW;gBAC7B,MAAM,aAAa,AAAC,UAAkB,UAAU;gBAChD,iBAAiB,WAAW,aAAa,IAAI,WAAW,IAAI,IAAI;gBAEhE,8CAA8C;gBAC9C,mBAAmB;oBAAC;oBAAW;iBAAK,CAAC,QAAQ,CAAC,WAAW,aAAa;YACxE;YAEA,iBAAiB;gBACf;gBACA;gBACA;YACF;QACF;QAEA,gBAAgB;QAChB;QAEA,mCAAmC;QACnC,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,gBAAgB,CAAC,WAAW;QAEnC,6CAA6C;QAC7C,IAAI,gBAAgB,WAAW;YAC7B,MAAM,aAAa,AAAC,UAAkB,UAAU;YAChD,WAAW,gBAAgB,CAAC,UAAU;QACxC;QAEA,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,OAAO,mBAAmB,CAAC,WAAW;YAEtC,IAAI,gBAAgB,WAAW;gBAC7B,MAAM,aAAa,AAAC,UAAkB,UAAU;gBAChD,WAAW,mBAAmB,CAAC,UAAU;YAC3C;QACF;IACF,GAAG,EAAE;IAEL,OAAO;AACT", "debugId": null}}, {"offset": {"line": 307, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/LiveTracking/src/components/ErrorBoundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\n\ninterface ErrorBoundaryState {\n  hasError: boolean\n  error?: Error\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode\n  fallback?: React.ComponentType<{ error: Error; resetError: () => void }>\n}\n\nclass ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return { hasError: true, error }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('Error caught by boundary:', error, errorInfo)\n  }\n\n  resetError = () => {\n    this.setState({ hasError: false, error: undefined })\n  }\n\n  render() {\n    if (this.state.hasError) {\n      const FallbackComponent = this.props.fallback || DefaultErrorFallback\n      return <FallbackComponent error={this.state.error!} resetError={this.resetError} />\n    }\n\n    return this.props.children\n  }\n}\n\nfunction DefaultErrorFallback({ error, resetError }: { error: Error; resetError: () => void }) {\n  return (\n    <div className=\"min-h-screen bg-red-50 flex items-center justify-center p-4\">\n      <div className=\"bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center\">\n        <div className=\"text-6xl mb-4\">⚠️</div>\n        <h1 className=\"text-2xl font-bold text-red-800 mb-4\">Something went wrong</h1>\n        <p className=\"text-red-600 mb-6\">\n          {error.message || 'An unexpected error occurred'}\n        </p>\n        <div className=\"space-y-3\">\n          <button\n            onClick={resetError}\n            className=\"w-full py-2 bg-red-500 text-white font-semibold rounded-lg hover:bg-red-600 transition-colors\"\n          >\n            Try Again\n          </button>\n          <button\n            onClick={() => window.location.href = '/'}\n            className=\"w-full py-2 bg-gray-500 text-white font-semibold rounded-lg hover:bg-gray-600 transition-colors\"\n          >\n            Go Home\n          </button>\n        </div>\n        <details className=\"mt-6 text-left\">\n          <summary className=\"cursor-pointer text-sm text-gray-500 hover:text-gray-700\">\n            Technical Details\n          </summary>\n          <pre className=\"mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto\">\n            {error.stack}\n          </pre>\n        </details>\n      </div>\n    </div>\n  )\n}\n\nexport default ErrorBoundary\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAcA,MAAM,sBAAsB,qMAAA,CAAA,UAAK,CAAC,SAAS;IACzC,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,6BAA6B,OAAO;IACpD;IAEA,aAAa;QACX,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;QAAU;IACpD,EAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI;YACjD,qBAAO,8OAAC;gBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBAAG,YAAY,IAAI,CAAC,UAAU;;;;;;QACjF;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAEA,SAAS,qBAAqB,EAAE,KAAK,EAAE,UAAU,EAA4C;IAC3F,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BAAgB;;;;;;8BAC/B,8OAAC;oBAAG,WAAU;8BAAuC;;;;;;8BACrD,8OAAC;oBAAE,WAAU;8BACV,MAAM,OAAO,IAAI;;;;;;8BAEpB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;sCAGD,8OAAC;4BACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;4BACtC,WAAU;sCACX;;;;;;;;;;;;8BAIH,8OAAC;oBAAQ,WAAU;;sCACjB,8OAAC;4BAAQ,WAAU;sCAA2D;;;;;;sCAG9E,8OAAC;4BAAI,WAAU;sCACZ,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;AAMxB;uCAEe", "debugId": null}}, {"offset": {"line": 461, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/LiveTracking/src/app/track/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport { LocationService } from '@/lib/locationService'\nimport { Location } from '@/lib/supabase'\nimport { useNetworkStatus } from '@/hooks/useNetworkStatus'\nimport ErrorBoundary from '@/components/ErrorBoundary'\nimport dynamic from 'next/dynamic'\n\n// Dynamically import MapComponent to avoid SSR issues\nconst MapComponent = dynamic(() => import('@/components/MapComponent'), {\n  ssr: false,\n  loading: () => (\n    <div className=\"h-96 w-full bg-gray-200 animate-pulse rounded-lg flex items-center justify-center\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2\"></div>\n        <p className=\"text-gray-600\">Loading map...</p>\n      </div>\n    </div>\n  )\n})\n\nfunction TrackPageContent() {\n  const [deviceId, setDeviceId] = useState('')\n  const [isConnected, setIsConnected] = useState(false)\n  const [currentLocation, setCurrentLocation] = useState<Location | null>(null)\n  const [error, setError] = useState<string | null>(null)\n  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected')\n  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)\n  const [updateCount, setUpdateCount] = useState(0)\n\n  const locationServiceRef = useRef<LocationService | null>(null)\n  const subscriptionRef = useRef<any>(null)\n  const networkStatus = useNetworkStatus()\n\n  useEffect(() => {\n    locationServiceRef.current = new LocationService()\n    \n    return () => {\n      if (subscriptionRef.current) {\n        subscriptionRef.current.unsubscribe()\n      }\n    }\n  }, [])\n\n  const connectToDevice = async () => {\n    if (!deviceId.trim()) {\n      setError('Please enter a valid Device ID')\n      return\n    }\n\n    if (!locationServiceRef.current) return\n\n    try {\n      setError(null)\n      setConnectionStatus('connecting')\n      \n      // First, try to get the latest location\n      const latestLocation = await locationServiceRef.current.getLatestLocation(deviceId.trim())\n      if (latestLocation) {\n        setCurrentLocation(latestLocation)\n        setLastUpdate(new Date(latestLocation.timestamp))\n      }\n\n      // Subscribe to real-time updates\n      subscriptionRef.current = locationServiceRef.current.subscribeToLocationUpdates(\n        deviceId.trim(),\n        (location) => {\n          setCurrentLocation(location)\n          setLastUpdate(new Date(location.timestamp))\n          setUpdateCount(prev => prev + 1)\n          setConnectionStatus('connected')\n          setIsConnected(true)\n        },\n        (error) => {\n          setError('Connection error: ' + error.message)\n          setConnectionStatus('disconnected')\n          setIsConnected(false)\n        }\n      )\n\n      setConnectionStatus('connected')\n      setIsConnected(true)\n      \n    } catch (error) {\n      setError(error instanceof Error ? error.message : 'Failed to connect')\n      setConnectionStatus('disconnected')\n      setIsConnected(false)\n    }\n  }\n\n  const disconnect = () => {\n    if (subscriptionRef.current) {\n      subscriptionRef.current.unsubscribe()\n      subscriptionRef.current = null\n    }\n    setIsConnected(false)\n    setConnectionStatus('disconnected')\n    setCurrentLocation(null)\n    setUpdateCount(0)\n    setLastUpdate(null)\n  }\n\n  const formatAccuracy = (accuracy: number) => {\n    return accuracy < 1000 ? `${Math.round(accuracy)}m` : `${(accuracy / 1000).toFixed(1)}km`\n  }\n\n  const getTimeSinceUpdate = () => {\n    if (!lastUpdate) return 'Never'\n    const now = new Date()\n    const diff = Math.floor((now.getTime() - lastUpdate.getTime()) / 1000)\n    \n    if (diff < 60) return `${diff}s ago`\n    if (diff < 3600) return `${Math.floor(diff / 60)}m ago`\n    return `${Math.floor(diff / 3600)}h ago`\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-green-50 to-blue-100\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-4xl mx-auto px-4 py-4\">\n          <h1 className=\"text-2xl font-bold text-gray-800\">🗺️ Live Location Tracker</h1>\n        </div>\n      </div>\n\n      <div className=\"max-w-4xl mx-auto p-4\">\n        {/* Connection Panel */}\n        <div className=\"bg-white rounded-lg shadow-lg p-6 mb-6\">\n          <h2 className=\"text-lg font-semibold mb-4\">Device Connection</h2>\n          \n          {!isConnected ? (\n            <div className=\"space-y-4\">\n              <div>\n                <label htmlFor=\"deviceId\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Device ID\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"deviceId\"\n                  value={deviceId}\n                  onChange={(e) => setDeviceId(e.target.value)}\n                  placeholder=\"Enter the Device ID from the admin page\"\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n              </div>\n              \n              <div className=\"space-y-2\">\n                <button\n                  onClick={connectToDevice}\n                  disabled={connectionStatus === 'connecting'}\n                  className=\"w-full py-2 bg-blue-500 text-white font-semibold rounded-lg hover:bg-blue-600 disabled:bg-blue-300 transition-colors\"\n                >\n                  {connectionStatus === 'connecting' ? '🔄 Connecting...' : '🔗 Connect to Device'}\n                </button>\n\n                {/* Test button for debugging */}\n                <button\n                  onClick={() => {\n                    console.log('Testing map with mock data...')\n                    setCurrentLocation({\n                      id: 'test',\n                      device_id: 'test-device',\n                      latitude: 37.7749,\n                      longitude: -122.4194,\n                      accuracy: 10,\n                      timestamp: new Date().toISOString(),\n                      created_at: new Date().toISOString()\n                    })\n                    setIsConnected(true)\n                  }}\n                  className=\"w-full py-2 bg-green-500 text-white font-semibold rounded-lg hover:bg-green-600 transition-colors text-sm\"\n                >\n                  🧪 Test Map (San Francisco)\n                </button>\n              </div>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm text-gray-600\">Connected to:</p>\n                  <code className=\"text-sm bg-gray-100 px-2 py-1 rounded\">{deviceId}</code>\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <span className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"></span>\n                  <span className=\"text-sm font-medium text-green-600\">Live</span>\n                </div>\n              </div>\n              \n              <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                <div>\n                  <span className=\"text-gray-600\">Updates received:</span>\n                  <span className=\"font-semibold ml-2\">{updateCount}</span>\n                </div>\n                <div>\n                  <span className=\"text-gray-600\">Last update:</span>\n                  <span className=\"font-semibold ml-2\">{getTimeSinceUpdate()}</span>\n                </div>\n              </div>\n              \n              <button\n                onClick={disconnect}\n                className=\"w-full py-2 bg-red-500 text-white font-semibold rounded-lg hover:bg-red-600 transition-colors\"\n              >\n                🔌 Disconnect\n              </button>\n            </div>\n          )}\n        </div>\n\n        {/* Error Display */}\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-6\">\n            <p className=\"text-red-800\">{error}</p>\n          </div>\n        )}\n\n        {/* Location Info and Map */}\n        {currentLocation && (\n          <div className=\"space-y-6\">\n            {/* Location Details */}\n            <div className=\"bg-white rounded-lg shadow-lg p-6\">\n              <h3 className=\"text-lg font-semibold mb-4\">📍 Current Location</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n                <div>\n                  <span className=\"text-gray-600\">Latitude:</span>\n                  <span className=\"font-mono ml-2\">{currentLocation.latitude.toFixed(6)}</span>\n                </div>\n                <div>\n                  <span className=\"text-gray-600\">Longitude:</span>\n                  <span className=\"font-mono ml-2\">{currentLocation.longitude.toFixed(6)}</span>\n                </div>\n                <div>\n                  <span className=\"text-gray-600\">Accuracy:</span>\n                  <span className=\"font-semibold ml-2\">{formatAccuracy(currentLocation.accuracy)}</span>\n                </div>\n                <div>\n                  <span className=\"text-gray-600\">Timestamp:</span>\n                  <span className=\"font-mono ml-2\">{new Date(currentLocation.timestamp).toLocaleString()}</span>\n                </div>\n              </div>\n            </div>\n\n            {/* Map */}\n            <div className=\"bg-white rounded-lg shadow-lg p-6\">\n              <div className=\"flex items-center justify-between mb-4\">\n                <h3 className=\"text-lg font-semibold\">🗺️ Live Map</h3>\n\n                {/* Map Legend */}\n                <div className=\"flex items-center gap-4 text-xs text-gray-600\">\n                  <div className=\"flex items-center gap-1\">\n                    <div className=\"w-4 h-4 bg-blue-500 bg-opacity-20 border-2 border-blue-500 rounded-full\"></div>\n                    <span>Accuracy Area</span>\n                  </div>\n                  <div className=\"flex items-center gap-1\">\n                    <div className=\"w-2 h-2 bg-red-600 rounded-full\"></div>\n                    <span>Exact Location</span>\n                  </div>\n                  <div className=\"flex items-center gap-1\">\n                    <div className=\"w-4 h-4 bg-blue-500 rounded-t-full\"></div>\n                    <span>Device Marker</span>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"rounded-lg overflow-hidden border\">\n                <MapComponent\n                  latitude={currentLocation.latitude}\n                  longitude={currentLocation.longitude}\n                  accuracy={currentLocation.accuracy}\n                  className=\"h-96 w-full\"\n                />\n              </div>\n\n              {/* Map Info */}\n              <div className=\"mt-3 text-xs text-gray-500 text-center\">\n                <p>🔴 Red dot shows the exact GPS coordinates • Blue circle shows accuracy range</p>\n                <p>Click on markers for detailed coordinates</p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Instructions */}\n        {!currentLocation && isConnected && (\n          <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-6\">\n            <h3 className=\"font-semibold text-yellow-800 mb-2\">⏳ Waiting for location data...</h3>\n            <p className=\"text-yellow-700\">\n              Make sure the admin device is broadcasting its location. The map will appear once location data is received.\n            </p>\n          </div>\n        )}\n\n        {!isConnected && (\n          <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-6\">\n            <h3 className=\"font-semibold text-blue-800 mb-2\">📋 How to use</h3>\n            <ol className=\"text-blue-700 space-y-1 list-decimal list-inside\">\n              <li>Get the Device ID from the admin page</li>\n              <li>Enter it in the field above</li>\n              <li>Click \"Connect to Device\"</li>\n              <li>View real-time location updates on the map</li>\n            </ol>\n          </div>\n        )}\n\n        {/* Network Status */}\n        {!networkStatus.isOnline && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-6\">\n            <h3 className=\"font-semibold text-red-800 mb-2\">📡 No internet connection</h3>\n            <p className=\"text-red-700\">\n              You're currently offline. Location updates will resume when your connection is restored.\n            </p>\n          </div>\n        )}\n\n        {networkStatus.isSlowConnection && networkStatus.isOnline && (\n          <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-6\">\n            <h3 className=\"font-semibold text-yellow-800 mb-2\">🐌 Slow connection detected</h3>\n            <p className=\"text-yellow-700\">\n              Your connection appears to be slow. Location updates may be delayed.\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n\nexport default function TrackPage() {\n  return (\n    <ErrorBoundary>\n      <TrackPageContent />\n    </ErrorBoundary>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;;AAPA;;;;;;;AASA,sDAAsD;AACtD,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IACzB,KAAK;IACL,SAAS,kBACP,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAMrC,SAAS;IACP,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IACxE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+C;IACtG,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC1D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0B;IAC1D,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IACpC,MAAM,gBAAgB,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD;IAErC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,mBAAmB,OAAO,GAAG,IAAI,6HAAA,CAAA,kBAAe;QAEhD,OAAO;YACL,IAAI,gBAAgB,OAAO,EAAE;gBAC3B,gBAAgB,OAAO,CAAC,WAAW;YACrC;QACF;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI,CAAC,SAAS,IAAI,IAAI;YACpB,SAAS;YACT;QACF;QAEA,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAEjC,IAAI;YACF,SAAS;YACT,oBAAoB;YAEpB,wCAAwC;YACxC,MAAM,iBAAiB,MAAM,mBAAmB,OAAO,CAAC,iBAAiB,CAAC,SAAS,IAAI;YACvF,IAAI,gBAAgB;gBAClB,mBAAmB;gBACnB,cAAc,IAAI,KAAK,eAAe,SAAS;YACjD;YAEA,iCAAiC;YACjC,gBAAgB,OAAO,GAAG,mBAAmB,OAAO,CAAC,0BAA0B,CAC7E,SAAS,IAAI,IACb,CAAC;gBACC,mBAAmB;gBACnB,cAAc,IAAI,KAAK,SAAS,SAAS;gBACzC,eAAe,CAAA,OAAQ,OAAO;gBAC9B,oBAAoB;gBACpB,eAAe;YACjB,GACA,CAAC;gBACC,SAAS,uBAAuB,MAAM,OAAO;gBAC7C,oBAAoB;gBACpB,eAAe;YACjB;YAGF,oBAAoB;YACpB,eAAe;QAEjB,EAAE,OAAO,OAAO;YACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,oBAAoB;YACpB,eAAe;QACjB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,gBAAgB,OAAO,EAAE;YAC3B,gBAAgB,OAAO,CAAC,WAAW;YACnC,gBAAgB,OAAO,GAAG;QAC5B;QACA,eAAe;QACf,oBAAoB;QACpB,mBAAmB;QACnB,eAAe;QACf,cAAc;IAChB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,WAAW,OAAO,GAAG,KAAK,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,GAAG,CAAC,WAAW,IAAI,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;IAC3F;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,YAAY,OAAO;QACxB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,WAAW,OAAO,EAAE,IAAI;QAEjE,IAAI,OAAO,IAAI,OAAO,GAAG,KAAK,KAAK,CAAC;QACpC,IAAI,OAAO,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,OAAO,IAAI,KAAK,CAAC;QACvD,OAAO,GAAG,KAAK,KAAK,CAAC,OAAO,MAAM,KAAK,CAAC;IAC1C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;;;;;;;;;;;0BAIrD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;4BAE1C,CAAC,4BACA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,SAAQ;gDAAW,WAAU;0DAA+C;;;;;;0DAGnF,8OAAC;gDACC,MAAK;gDACL,IAAG;gDACH,OAAO;gDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC3C,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAId,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS;gDACT,UAAU,qBAAqB;gDAC/B,WAAU;0DAET,qBAAqB,eAAe,qBAAqB;;;;;;0DAI5D,8OAAC;gDACC,SAAS;oDACP,QAAQ,GAAG,CAAC;oDACZ,mBAAmB;wDACjB,IAAI;wDACJ,WAAW;wDACX,UAAU;wDACV,WAAW,CAAC;wDACZ,UAAU;wDACV,WAAW,IAAI,OAAO,WAAW;wDACjC,YAAY,IAAI,OAAO,WAAW;oDACpC;oDACA,eAAe;gDACjB;gDACA,WAAU;0DACX;;;;;;;;;;;;;;;;;qDAML,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;kEACrC,8OAAC;wDAAK,WAAU;kEAAyC;;;;;;;;;;;;0DAE3D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;kEAAqC;;;;;;;;;;;;;;;;;;kDAIzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEAAsB;;;;;;;;;;;;0DAExC,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEAAsB;;;;;;;;;;;;;;;;;;kDAI1C,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;oBAQN,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;oBAKhC,iCACC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEAAkB,gBAAgB,QAAQ,CAAC,OAAO,CAAC;;;;;;;;;;;;0DAErE,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEAAkB,gBAAgB,SAAS,CAAC,OAAO,CAAC;;;;;;;;;;;;0DAEtE,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEAAsB,eAAe,gBAAgB,QAAQ;;;;;;;;;;;;0DAE/E,8OAAC;;kEACC,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEAAkB,IAAI,KAAK,gBAAgB,SAAS,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;;0CAM1F,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAwB;;;;;;0DAGtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;;kEAER,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;0EAAK;;;;;;;;;;;;;;;;;;;;;;;;kDAKZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,UAAU,gBAAgB,QAAQ;4CAClC,WAAW,gBAAgB,SAAS;4CACpC,UAAU,gBAAgB,QAAQ;4CAClC,WAAU;;;;;;;;;;;kDAKd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAE;;;;;;0DACH,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;oBAOV,CAAC,mBAAmB,6BACnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,8OAAC;gCAAE,WAAU;0CAAkB;;;;;;;;;;;;oBAMlC,CAAC,6BACA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;oBAMT,CAAC,cAAc,QAAQ,kBACtB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAkC;;;;;;0CAChD,8OAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;oBAM/B,cAAc,gBAAgB,IAAI,cAAc,QAAQ,kBACvD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,8OAAC;gCAAE,WAAU;0CAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAQ3C;AAEe,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,UAAa;kBACZ,cAAA,8OAAC;;;;;;;;;;AAGP", "debugId": null}}]}