// Global type definitions for the Live Tracking application

interface WakeLockSentinel {
  readonly released: boolean;
  readonly type: WakeLockType;
  release(): Promise<void>;
  addEventListener(type: 'release', listener: (event: Event) => void): void;
  removeEventListener(type: 'release', listener: (event: Event) => void): void;
}

type WakeLockType = 'screen';

interface WakeLock {
  request(type: WakeLockType): Promise<WakeLockSentinel>;
}

interface Navigator {
  wakeLock?: WakeLock;
}

// Extend the global Window interface
declare global {
  interface Window {
    // Add any window-specific properties if needed
  }
}

export {};
