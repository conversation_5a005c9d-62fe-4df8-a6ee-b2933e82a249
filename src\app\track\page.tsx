'use client'

import { useState, useEffect, useRef } from 'react'
import { LocationService } from '@/lib/locationService'
import { Location } from '@/lib/supabase'
import { useNetworkStatus } from '@/hooks/useNetworkStatus'
import ErrorBoundary from '@/components/ErrorBoundary'
import dynamic from 'next/dynamic'

// Dynamically import MapComponent to avoid SSR issues
const MapComponent = dynamic(() => import('@/components/MapComponent'), {
  ssr: false,
  loading: () => <div className="h-96 w-full bg-gray-200 animate-pulse rounded-lg flex items-center justify-center">Loading map...</div>
})

function TrackPageContent() {
  const [deviceId, setDeviceId] = useState('')
  const [isConnected, setIsConnected] = useState(false)
  const [currentLocation, setCurrentLocation] = useState<Location | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected')
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)
  const [updateCount, setUpdateCount] = useState(0)

  const locationServiceRef = useRef<LocationService | null>(null)
  const subscriptionRef = useRef<any>(null)
  const networkStatus = useNetworkStatus()

  useEffect(() => {
    locationServiceRef.current = new LocationService()
    
    return () => {
      if (subscriptionRef.current) {
        subscriptionRef.current.unsubscribe()
      }
    }
  }, [])

  const connectToDevice = async () => {
    if (!deviceId.trim()) {
      setError('Please enter a valid Device ID')
      return
    }

    if (!locationServiceRef.current) return

    try {
      setError(null)
      setConnectionStatus('connecting')
      
      // First, try to get the latest location
      const latestLocation = await locationServiceRef.current.getLatestLocation(deviceId.trim())
      if (latestLocation) {
        setCurrentLocation(latestLocation)
        setLastUpdate(new Date(latestLocation.timestamp))
      }

      // Subscribe to real-time updates
      subscriptionRef.current = locationServiceRef.current.subscribeToLocationUpdates(
        deviceId.trim(),
        (location) => {
          setCurrentLocation(location)
          setLastUpdate(new Date(location.timestamp))
          setUpdateCount(prev => prev + 1)
          setConnectionStatus('connected')
          setIsConnected(true)
        },
        (error) => {
          setError('Connection error: ' + error.message)
          setConnectionStatus('disconnected')
          setIsConnected(false)
        }
      )

      setConnectionStatus('connected')
      setIsConnected(true)
      
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to connect')
      setConnectionStatus('disconnected')
      setIsConnected(false)
    }
  }

  const disconnect = () => {
    if (subscriptionRef.current) {
      subscriptionRef.current.unsubscribe()
      subscriptionRef.current = null
    }
    setIsConnected(false)
    setConnectionStatus('disconnected')
    setCurrentLocation(null)
    setUpdateCount(0)
    setLastUpdate(null)
  }

  const formatAccuracy = (accuracy: number) => {
    return accuracy < 1000 ? `${Math.round(accuracy)}m` : `${(accuracy / 1000).toFixed(1)}km`
  }

  const getTimeSinceUpdate = () => {
    if (!lastUpdate) return 'Never'
    const now = new Date()
    const diff = Math.floor((now.getTime() - lastUpdate.getTime()) / 1000)
    
    if (diff < 60) return `${diff}s ago`
    if (diff < 3600) return `${Math.floor(diff / 60)}m ago`
    return `${Math.floor(diff / 3600)}h ago`
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <h1 className="text-2xl font-bold text-gray-800">🗺️ Live Location Tracker</h1>
        </div>
      </div>

      <div className="max-w-4xl mx-auto p-4">
        {/* Connection Panel */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-lg font-semibold mb-4">Device Connection</h2>
          
          {!isConnected ? (
            <div className="space-y-4">
              <div>
                <label htmlFor="deviceId" className="block text-sm font-medium text-gray-700 mb-2">
                  Device ID
                </label>
                <input
                  type="text"
                  id="deviceId"
                  value={deviceId}
                  onChange={(e) => setDeviceId(e.target.value)}
                  placeholder="Enter the Device ID from the admin page"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <button
                onClick={connectToDevice}
                disabled={connectionStatus === 'connecting'}
                className="w-full py-2 bg-blue-500 text-white font-semibold rounded-lg hover:bg-blue-600 disabled:bg-blue-300 transition-colors"
              >
                {connectionStatus === 'connecting' ? '🔄 Connecting...' : '🔗 Connect to Device'}
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Connected to:</p>
                  <code className="text-sm bg-gray-100 px-2 py-1 rounded">{deviceId}</code>
                </div>
                <div className="flex items-center gap-2">
                  <span className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></span>
                  <span className="text-sm font-medium text-green-600">Live</span>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Updates received:</span>
                  <span className="font-semibold ml-2">{updateCount}</span>
                </div>
                <div>
                  <span className="text-gray-600">Last update:</span>
                  <span className="font-semibold ml-2">{getTimeSinceUpdate()}</span>
                </div>
              </div>
              
              <button
                onClick={disconnect}
                className="w-full py-2 bg-red-500 text-white font-semibold rounded-lg hover:bg-red-600 transition-colors"
              >
                🔌 Disconnect
              </button>
            </div>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* Location Info and Map */}
        {currentLocation && (
          <div className="space-y-6">
            {/* Location Details */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold mb-4">📍 Current Location</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Latitude:</span>
                  <span className="font-mono ml-2">{currentLocation.latitude.toFixed(6)}</span>
                </div>
                <div>
                  <span className="text-gray-600">Longitude:</span>
                  <span className="font-mono ml-2">{currentLocation.longitude.toFixed(6)}</span>
                </div>
                <div>
                  <span className="text-gray-600">Accuracy:</span>
                  <span className="font-semibold ml-2">{formatAccuracy(currentLocation.accuracy)}</span>
                </div>
                <div>
                  <span className="text-gray-600">Timestamp:</span>
                  <span className="font-mono ml-2">{new Date(currentLocation.timestamp).toLocaleString()}</span>
                </div>
              </div>
            </div>

            {/* Map */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold mb-4">🗺️ Live Map</h3>
              <div className="rounded-lg overflow-hidden border">
                <MapComponent
                  latitude={currentLocation.latitude}
                  longitude={currentLocation.longitude}
                  accuracy={currentLocation.accuracy}
                  className="h-96 w-full"
                />
              </div>
            </div>
          </div>
        )}

        {/* Instructions */}
        {!currentLocation && isConnected && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="font-semibold text-yellow-800 mb-2">⏳ Waiting for location data...</h3>
            <p className="text-yellow-700">
              Make sure the admin device is broadcasting its location. The map will appear once location data is received.
            </p>
          </div>
        )}

        {!isConnected && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 className="font-semibold text-blue-800 mb-2">📋 How to use</h3>
            <ol className="text-blue-700 space-y-1 list-decimal list-inside">
              <li>Get the Device ID from the admin page</li>
              <li>Enter it in the field above</li>
              <li>Click "Connect to Device"</li>
              <li>View real-time location updates on the map</li>
            </ol>
          </div>
        )}
      </div>
    </div>
  )
}
