@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Leaflet map styles */
.leaflet-container {
  height: 100%;
  width: 100%;
}

/* Fix for Leaflet popup z-index issues */
.leaflet-popup-pane {
  z-index: 1000;
}

/* Custom marker styles */
.leaflet-marker-icon {
  filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.3));
}
