// Service Worker for Live Tracking App
// Helps with background execution and caching

const CACHE_NAME = 'live-tracking-v1';
const urlsToCache = [
  '/',
  '/admin',
  '/track',
  '/manifest.json'
];

// Install event - cache resources
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        return cache.addAll(urlsToCache);
      })
  );
});

// Fetch event - serve from cache when offline
self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        return response || fetch(event.request);
      }
    )
  );
});

// Background sync for location updates (if supported)
self.addEventListener('sync', (event) => {
  if (event.tag === 'location-sync') {
    event.waitUntil(handleBackgroundLocationSync());
  }
});

// Handle background location sync
async function handleBackgroundLocationSync() {
  console.log('Background sync triggered for location updates');

  try {
    // Try to get current location in background
    if ('geolocation' in navigator) {
      const position = await getCurrentPosition();

      // Send location update message to main thread
      const clients = await self.clients.matchAll();
      clients.forEach(client => {
        client.postMessage({
          type: 'BACKGROUND_LOCATION_UPDATE',
          location: {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: new Date().toISOString()
          }
        });
      });
    }
  } catch (error) {
    console.error('Background location sync failed:', error);
  }
}

// Helper function to get current position as Promise
function getCurrentPosition() {
  return new Promise((resolve, reject) => {
    navigator.geolocation.getCurrentPosition(
      resolve,
      reject,
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0
      }
    );
  });
}

// Handle push notifications (for future use)
self.addEventListener('push', (event) => {
  const options = {
    body: event.data ? event.data.text() : 'Location update received',
    icon: '/icon-192x192.png',
    badge: '/icon-192x192.png'
  };

  event.waitUntil(
    self.registration.showNotification('Live Tracking', options)
  );
});
