// Service Worker for Live Tracking App
// Helps with background execution and caching

const CACHE_NAME = 'live-tracking-v1';
const urlsToCache = [
  '/',
  '/admin',
  '/track',
  '/manifest.json'
];

// Install event - cache resources
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        return cache.addAll(urlsToCache);
      })
  );
});

// Fetch event - serve from cache when offline
self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        return response || fetch(event.request);
      }
    )
  );
});

// Background sync for location updates (if supported)
self.addEventListener('sync', (event) => {
  if (event.tag === 'location-sync') {
    event.waitUntil(
      // Handle background location sync
      console.log('Background sync triggered for location updates')
    );
  }
});

// Handle push notifications (for future use)
self.addEventListener('push', (event) => {
  const options = {
    body: event.data ? event.data.text() : 'Location update received',
    icon: '/icon-192x192.png',
    badge: '/icon-192x192.png'
  };

  event.waitUntil(
    self.registration.showNotification('Live Tracking', options)
  );
});
