'use client'

import { useState, useEffect, useRef } from 'react'
import { LocationService, LocationData } from '@/lib/locationService'
import { useNetworkStatus } from '@/hooks/useNetworkStatus'
import ErrorBoundary from '@/components/ErrorBoundary'

function AdminPageContent() {
  const [isTracking, setIsTracking] = useState(false)
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [deviceId, setDeviceId] = useState<string>('')
  const [locationCount, setLocationCount] = useState(0)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)
  const [isBackgroundActive, setIsBackgroundActive] = useState(false)

  const locationServiceRef = useRef<LocationService | null>(null)
  const wakeLockRef = useRef<WakeLockSentinel | null>(null)
  const networkStatus = useNetworkStatus()

  useEffect(() => {
    locationServiceRef.current = new LocationService()
    setDeviceId(locationServiceRef.current.getDeviceId())

    // Listen for background location updates from service worker
    const handleServiceWorkerMessage = (event: MessageEvent) => {
      if (event.data?.type === 'BACKGROUND_LOCATION_UPDATE') {
        const location = event.data.location
        console.log('Received background location update:', location)

        setCurrentLocation({
          latitude: location.latitude,
          longitude: location.longitude,
          accuracy: location.accuracy,
          timestamp: new Date(location.timestamp)
        })
        setLocationCount(prev => prev + 1)
        setLastUpdate(new Date())
      }
    }

    navigator.serviceWorker?.addEventListener('message', handleServiceWorkerMessage)

    // Handle visibility changes to show background status
    const handleVisibilityChange = () => {
      const isHidden = document.hidden
      setIsBackgroundActive(isHidden && isTracking)
      console.log(`Tab ${isHidden ? 'hidden' : 'visible'} - Background tracking: ${isHidden && isTracking}`)
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      if (locationServiceRef.current) {
        locationServiceRef.current.stopTracking()
      }
      releaseWakeLock()
      navigator.serviceWorker?.removeEventListener('message', handleServiceWorkerMessage)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [])

  // Update background status when tracking state changes
  useEffect(() => {
    setIsBackgroundActive(document.hidden && isTracking)
  }, [isTracking])

  const requestWakeLock = async () => {
    try {
      if ('wakeLock' in navigator) {
        wakeLockRef.current = await navigator.wakeLock.request('screen')
        console.log('Wake lock acquired')
      }
    } catch (error) {
      console.error('Wake lock failed:', error)
    }
  }

  const releaseWakeLock = () => {
    if (wakeLockRef.current) {
      wakeLockRef.current.release()
      wakeLockRef.current = null
      console.log('Wake lock released')
    }
  }

  const startTracking = async () => {
    if (!locationServiceRef.current) return

    try {
      setError(null)
      setIsTracking(true)
      
      await requestWakeLock()
      
      await locationServiceRef.current.startTracking((location) => {
        setCurrentLocation(location)
        setLocationCount(prev => prev + 1)
        setLastUpdate(new Date())
      })
      
      console.log('Location tracking started')
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to start tracking')
      setIsTracking(false)
      releaseWakeLock()
    }
  }

  const stopTracking = () => {
    if (locationServiceRef.current) {
      locationServiceRef.current.stopTracking()
      setIsTracking(false)
      releaseWakeLock()
      console.log('Location tracking stopped')
    }
  }

  const copyDeviceId = () => {
    navigator.clipboard.writeText(deviceId)
    alert('Device ID copied to clipboard!')
  }

  const formatAccuracy = (accuracy: number) => {
    return accuracy < 1000 ? `${Math.round(accuracy)}m` : `${(accuracy / 1000).toFixed(1)}km`
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-md mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-6 text-center">
            📍 Location Admin
          </h1>

          {/* Device ID Section */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h2 className="text-sm font-semibold text-gray-600 mb-2">Device ID</h2>
            <div className="flex items-center gap-2">
              <code className="flex-1 text-xs bg-white p-2 rounded border font-mono">
                {deviceId}
              </code>
              <button
                onClick={copyDeviceId}
                className="px-3 py-2 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors"
              >
                Copy
              </button>
            </div>
          </div>

          {/* Status Section */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <span className="text-lg font-semibold">Status:</span>
              <div className="flex items-center gap-2">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  isTracking
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {isTracking ? '🟢 Active' : '🔴 Inactive'}
                </span>
                {isBackgroundActive && (
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 flex items-center gap-1">
                    <span className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></span>
                    Background
                  </span>
                )}
              </div>
            </div>

            {isTracking && (
              <div className="space-y-2 text-sm text-gray-600">
                <div>Updates sent: <span className="font-semibold">{locationCount}</span></div>
                {lastUpdate && (
                  <div>Last update: <span className="font-semibold">
                    {lastUpdate.toLocaleTimeString()}
                  </span></div>
                )}
              </div>
            )}
          </div>

          {/* Current Location */}
          {currentLocation && (
            <div className="mb-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold text-blue-800 mb-2">Current Location</h3>
              <div className="space-y-1 text-sm">
                <div>Lat: <span className="font-mono">{currentLocation.latitude.toFixed(6)}</span></div>
                <div>Lng: <span className="font-mono">{currentLocation.longitude.toFixed(6)}</span></div>
                <div>Accuracy: <span className="font-semibold">{formatAccuracy(currentLocation.accuracy)}</span></div>
                <div>Time: <span className="font-mono">{currentLocation.timestamp.toLocaleTimeString()}</span></div>
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          {/* Control Buttons */}
          <div className="space-y-3">
            {!isTracking ? (
              <button
                onClick={startTracking}
                className="w-full py-3 bg-green-500 text-white font-semibold rounded-lg hover:bg-green-600 transition-colors"
              >
                🚀 Start Broadcasting Location
              </button>
            ) : (
              <button
                onClick={stopTracking}
                className="w-full py-3 bg-red-500 text-white font-semibold rounded-lg hover:bg-red-600 transition-colors"
              >
                ⏹️ Stop Broadcasting
              </button>
            )}
          </div>

          {/* Instructions */}
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h3 className="font-semibold text-yellow-800 mb-2">📋 Instructions</h3>
            <ol className="text-sm text-yellow-700 space-y-1 list-decimal list-inside">
              <li>Copy your Device ID above</li>
              <li>Share it with the tracking device</li>
              <li>Click "Start Broadcasting" to begin</li>
              <li>Location continues broadcasting even when tab is hidden</li>
              <li>Background mode indicator shows when tab is not active</li>
            </ol>
          </div>

          {/* Network Status */}
          {!networkStatus.isOnline && (
            <div className="mt-4 p-3 bg-red-100 border border-red-300 rounded-lg">
              <p className="text-red-800 text-sm font-medium">📡 No internet connection</p>
              <p className="text-red-600 text-xs">Location updates will resume when connection is restored</p>
            </div>
          )}

          {networkStatus.isSlowConnection && networkStatus.isOnline && (
            <div className="mt-4 p-3 bg-yellow-100 border border-yellow-300 rounded-lg">
              <p className="text-yellow-800 text-sm font-medium">🐌 Slow connection detected</p>
              <p className="text-yellow-600 text-xs">Location updates may be delayed</p>
            </div>
          )}

          {/* Technical Info */}
          <div className="mt-4 text-xs text-gray-500 text-center space-y-1">
            <p>⚡ Wake lock: {wakeLockRef.current ? 'Active' : 'Inactive'}</p>
            <p>🔄 Updates every ~1 second when active</p>
            <p>📡 Network: {networkStatus.isOnline ? 'Online' : 'Offline'} ({networkStatus.connectionType})</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function AdminPage() {
  return (
    <ErrorBoundary>
      <AdminPageContent />
    </ErrorBoundary>
  )
}
