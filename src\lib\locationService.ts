import { supabase, Location } from './supabase'
import { v4 as uuidv4 } from 'uuid'

export interface LocationData {
  latitude: number
  longitude: number
  accuracy: number
  timestamp: Date
}

export class LocationService {
  private deviceId: string
  private watchId: number | null = null
  private isTracking = false
  private backgroundInterval: NodeJS.Timeout | null = null
  private lastLocationTime = 0
  private visibilityChangeHandler: (() => void) | null = null
  private locationWorker: Worker | null = null
  private workerMessageHandler: ((event: MessageEvent) => void) | null = null
  private heartbeatInterval: NodeJS.Timeout | null = null

  constructor(deviceId?: string) {
    this.deviceId = deviceId || this.generateDeviceId()
  }

  private generateDeviceId(): string {
    let deviceId = localStorage.getItem('device_id')
    if (!deviceId) {
      deviceId = uuidv4()
      localStorage.setItem('device_id', deviceId)
    }
    return deviceId
  }

  async startTracking(onLocationUpdate?: (location: LocationData) => void): Promise<void> {
    if (this.isTracking) {
      console.log('Already tracking location')
      return
    }

    if (!navigator.geolocation) {
      throw new Error('Geolocation is not supported by this browser')
    }

    this.isTracking = true

    // Start primary geolocation tracking
    await this.startGeolocationWatch(onLocationUpdate)

    // Start background interval for when tab is not active
    this.startBackgroundTracking(onLocationUpdate)

    // Start Web Worker for reliable background tracking
    this.startLocationWorker(onLocationUpdate)

    // Handle visibility changes to ensure continuous tracking
    this.setupVisibilityHandling(onLocationUpdate)

    // Start heartbeat to keep connection alive
    this.startHeartbeat()

    console.log('Location tracking started with background support')
  }

  private async startGeolocationWatch(onLocationUpdate?: (location: LocationData) => void): Promise<void> {
    return new Promise((resolve, reject) => {
      const options: PositionOptions = {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 0 // Always get fresh location
      }

      this.watchId = navigator.geolocation.watchPosition(
        async (position) => {
          const locationData: LocationData = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: new Date()
          }

          this.lastLocationTime = Date.now()

          try {
            await this.saveLocation(locationData)
            onLocationUpdate?.(locationData)
            console.log('Location updated via watchPosition')
            resolve()
          } catch (error) {
            console.error('Error saving location:', error)
          }
        },
        (error) => {
          console.error('Geolocation error:', error)
          // Don't reject, continue with background tracking
          resolve()
        },
        options
      )
    })
  }

  private startBackgroundTracking(onLocationUpdate?: (location: LocationData) => void): void {
    // Clear any existing interval
    if (this.backgroundInterval) {
      clearInterval(this.backgroundInterval)
    }

    // Set up very aggressive background tracking every 1 second
    this.backgroundInterval = setInterval(async () => {
      if (!this.isTracking) return

      // Always try to get location when tab is hidden
      if (document.hidden) {
        console.log('Tab hidden - forcing location update')
        await this.getCurrentLocationForce(onLocationUpdate)
      } else {
        // Even when visible, check if we haven't received updates recently
        const timeSinceLastUpdate = Date.now() - this.lastLocationTime
        if (timeSinceLastUpdate > 2000) { // 2 seconds
          console.log('No recent updates - forcing location update')
          await this.getCurrentLocationForce(onLocationUpdate)
        }
      }
    }, 1000) // Every 1 second - very aggressive
  }

  private async getCurrentLocationForce(onLocationUpdate?: (location: LocationData) => void): Promise<void> {
    return new Promise((resolve) => {
      // Try multiple approaches for background location
      const options: PositionOptions = {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 0
      }

      // First try getCurrentPosition
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const locationData: LocationData = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: new Date()
          }

          this.lastLocationTime = Date.now()

          try {
            await this.saveLocation(locationData)
            onLocationUpdate?.(locationData)
            console.log('Location updated via getCurrentPosition (background)')
          } catch (error) {
            console.error('Error saving background location:', error)
          }
          resolve()
        },
        async (error) => {
          console.error('Background geolocation error:', error)

          // Fallback: Try to use service worker for background location
          if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
            try {
              console.log('Attempting service worker background location')
              navigator.serviceWorker.controller.postMessage({
                type: 'REQUEST_LOCATION',
                deviceId: this.deviceId
              })
            } catch (swError) {
              console.error('Service worker location request failed:', swError)
            }
          }

          resolve()
        },
        options
      )
    })
  }

  private setupVisibilityHandling(onLocationUpdate?: (location: LocationData) => void): void {
    // Clean up existing handler
    if (this.visibilityChangeHandler) {
      document.removeEventListener('visibilitychange', this.visibilityChangeHandler)
    }

    this.visibilityChangeHandler = async () => {
      if (document.hidden) {
        console.log('Tab became hidden - ensuring background tracking')
        // Immediately get location when tab becomes hidden
        await this.getCurrentLocationForce(onLocationUpdate)
      } else {
        console.log('Tab became visible - resuming normal tracking')
        // Restart geolocation watch when tab becomes visible
        if (this.watchId) {
          navigator.geolocation.clearWatch(this.watchId)
        }
        await this.startGeolocationWatch(onLocationUpdate)
      }
    }

    document.addEventListener('visibilitychange', this.visibilityChangeHandler)
  }

  private startHeartbeat(): void {
    // Clear any existing heartbeat
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
    }

    // Send a heartbeat every 30 seconds to keep connection alive
    this.heartbeatInterval = setInterval(async () => {
      if (this.isTracking) {
        try {
          // Just ping the database to keep connection alive
          await fetch(`${this.getSupabaseUrl()}/rest/v1/locations?select=count&limit=1`, {
            method: 'GET',
            headers: {
              'apikey': this.getSupabaseKey(),
              'Authorization': `Bearer ${this.getSupabaseKey()}`
            }
          })
          console.log('Heartbeat sent')
        } catch (error) {
          console.error('Heartbeat failed:', error)
        }
      }
    }, 30000) // Every 30 seconds
  }

  private getSupabaseUrl(): string {
    return 'https://zlmzyxdrojzwzqehslsz.supabase.co'
  }

  private getSupabaseKey(): string {
    return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpsbXp5eGRyb2p6d3pxZWhzbHN6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3ODkxMTksImV4cCI6MjA2NzM2NTExOX0.usKASd9XHanrwdhtxsoo0OofC873i_CuJLvdhtNzJqY'
  }

  private startLocationWorker(onLocationUpdate?: (location: LocationData) => void): void {
    try {
      // Create Web Worker for background location tracking
      this.locationWorker = new Worker('/location-worker.js')

      this.workerMessageHandler = (event: MessageEvent) => {
        const { type, location, error } = event.data

        switch (type) {
          case 'LOCATION_UPDATE':
            console.log('Received location from worker:', location)
            if (location && onLocationUpdate) {
              const locationData: LocationData = {
                latitude: location.latitude,
                longitude: location.longitude,
                accuracy: location.accuracy,
                timestamp: new Date(location.timestamp)
              }
              onLocationUpdate(locationData)
              this.lastLocationTime = Date.now()
            }
            break
          case 'LOCATION_ERROR':
            console.error('Worker location error:', error)
            break
          case 'WORKER_ERROR':
            console.error('Worker error:', error)
            break
          case 'TRACKING_STOPPED':
            console.log('Worker tracking stopped')
            break
        }
      }

      this.locationWorker.addEventListener('message', this.workerMessageHandler)

      // Start worker tracking with Supabase configuration
      const supabaseUrl = this.getSupabaseUrl()
      const supabaseKey = this.getSupabaseKey()

      this.locationWorker.postMessage({
        type: 'START_TRACKING',
        data: {
          deviceId: this.deviceId,
          supabaseUrl: supabaseUrl,
          supabaseKey: supabaseKey
        }
      })

      console.log('Location worker started')
    } catch (error) {
      console.error('Failed to start location worker:', error)
    }
  }

  private stopLocationWorker(): void {
    if (this.locationWorker) {
      this.locationWorker.postMessage({ type: 'STOP_TRACKING' })

      if (this.workerMessageHandler) {
        this.locationWorker.removeEventListener('message', this.workerMessageHandler)
        this.workerMessageHandler = null
      }

      this.locationWorker.terminate()
      this.locationWorker = null
      console.log('Location worker stopped')
    }
  }

  stopTracking(): void {
    console.log('Stopping all location tracking')

    // Clear geolocation watch
    if (this.watchId !== null) {
      navigator.geolocation.clearWatch(this.watchId)
      this.watchId = null
    }

    // Clear background interval
    if (this.backgroundInterval) {
      clearInterval(this.backgroundInterval)
      this.backgroundInterval = null
    }

    // Clear heartbeat interval
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }

    // Stop location worker
    this.stopLocationWorker()

    // Remove visibility change handler
    if (this.visibilityChangeHandler) {
      document.removeEventListener('visibilitychange', this.visibilityChangeHandler)
      this.visibilityChangeHandler = null
    }

    this.isTracking = false
  }

  private async saveLocation(locationData: LocationData): Promise<void> {
    const { error } = await supabase
      .from('locations')
      .upsert({
        device_id: this.deviceId,
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        accuracy: locationData.accuracy,
        timestamp: locationData.timestamp.toISOString()
      }, {
        onConflict: 'device_id'
      })

    if (error) {
      throw error
    }
  }

  async getLatestLocation(deviceId?: string): Promise<Location | null> {
    const targetDeviceId = deviceId || this.deviceId
    
    const { data, error } = await supabase
      .from('locations')
      .select('*')
      .eq('device_id', targetDeviceId)
      .order('timestamp', { ascending: false })
      .limit(1)
      .single()

    if (error && error.code !== 'PGRST116') {
      throw error
    }

    return data || null
  }

  subscribeToLocationUpdates(
    deviceId: string,
    onUpdate: (location: Location) => void,
    onError?: (error: any) => void
  ) {
    return supabase
      .channel(`location-updates-${deviceId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'locations',
          filter: `device_id=eq.${deviceId}`
        },
        (payload) => {
          if (payload.new) {
            onUpdate(payload.new as Location)
          }
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('Subscribed to location updates')
        } else if (status === 'CHANNEL_ERROR') {
          console.error('Error subscribing to location updates')
          onError?.(new Error('Subscription failed'))
        }
      })
  }

  getDeviceId(): string {
    return this.deviceId
  }

  isCurrentlyTracking(): boolean {
    return this.isTracking
  }
}
