import { supabase, Location } from './supabase'
import { v4 as uuidv4 } from 'uuid'

export interface LocationData {
  latitude: number
  longitude: number
  accuracy: number
  timestamp: Date
}

export class LocationService {
  private deviceId: string
  private watchId: number | null = null
  private isTracking = false
  private backgroundInterval: NodeJS.Timeout | null = null
  private lastLocationTime = 0
  private visibilityChangeHandler: (() => void) | null = null

  constructor(deviceId?: string) {
    this.deviceId = deviceId || this.generateDeviceId()
  }

  private generateDeviceId(): string {
    let deviceId = localStorage.getItem('device_id')
    if (!deviceId) {
      deviceId = uuidv4()
      localStorage.setItem('device_id', deviceId)
    }
    return deviceId
  }

  async startTracking(onLocationUpdate?: (location: LocationData) => void): Promise<void> {
    if (this.isTracking) {
      console.log('Already tracking location')
      return
    }

    if (!navigator.geolocation) {
      throw new Error('Geolocation is not supported by this browser')
    }

    this.isTracking = true

    // Start primary geolocation tracking
    await this.startGeolocationWatch(onLocationUpdate)

    // Start background interval for when tab is not active
    this.startBackgroundTracking(onLocationUpdate)

    // Handle visibility changes to ensure continuous tracking
    this.setupVisibilityHandling(onLocationUpdate)

    console.log('Location tracking started with background support')
  }

  private async startGeolocationWatch(onLocationUpdate?: (location: LocationData) => void): Promise<void> {
    return new Promise((resolve, reject) => {
      const options: PositionOptions = {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 0 // Always get fresh location
      }

      this.watchId = navigator.geolocation.watchPosition(
        async (position) => {
          const locationData: LocationData = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: new Date()
          }

          this.lastLocationTime = Date.now()

          try {
            await this.saveLocation(locationData)
            onLocationUpdate?.(locationData)
            console.log('Location updated via watchPosition')
            resolve()
          } catch (error) {
            console.error('Error saving location:', error)
          }
        },
        (error) => {
          console.error('Geolocation error:', error)
          // Don't reject, continue with background tracking
          resolve()
        },
        options
      )
    })
  }

  private startBackgroundTracking(onLocationUpdate?: (location: LocationData) => void): void {
    // Clear any existing interval
    if (this.backgroundInterval) {
      clearInterval(this.backgroundInterval)
    }

    // Set up aggressive background tracking every 2 seconds
    this.backgroundInterval = setInterval(async () => {
      if (!this.isTracking) return

      // Check if we haven't received a location update recently
      const timeSinceLastUpdate = Date.now() - this.lastLocationTime
      const shouldForceUpdate = timeSinceLastUpdate > 3000 // 3 seconds

      if (shouldForceUpdate || document.hidden) {
        console.log('Forcing location update (background/hidden tab)')
        await this.getCurrentLocationForce(onLocationUpdate)
      }
    }, 2000) // Every 2 seconds
  }

  private async getCurrentLocationForce(onLocationUpdate?: (location: LocationData) => void): Promise<void> {
    return new Promise((resolve) => {
      const options: PositionOptions = {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 0
      }

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const locationData: LocationData = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: new Date()
          }

          this.lastLocationTime = Date.now()

          try {
            await this.saveLocation(locationData)
            onLocationUpdate?.(locationData)
            console.log('Location updated via getCurrentPosition (background)')
          } catch (error) {
            console.error('Error saving background location:', error)
          }
          resolve()
        },
        (error) => {
          console.error('Background geolocation error:', error)
          resolve()
        },
        options
      )
    })
  }

  private setupVisibilityHandling(onLocationUpdate?: (location: LocationData) => void): void {
    // Clean up existing handler
    if (this.visibilityChangeHandler) {
      document.removeEventListener('visibilitychange', this.visibilityChangeHandler)
    }

    this.visibilityChangeHandler = async () => {
      if (document.hidden) {
        console.log('Tab became hidden - ensuring background tracking')
        // Immediately get location when tab becomes hidden
        await this.getCurrentLocationForce(onLocationUpdate)
      } else {
        console.log('Tab became visible - resuming normal tracking')
        // Restart geolocation watch when tab becomes visible
        if (this.watchId) {
          navigator.geolocation.clearWatch(this.watchId)
        }
        await this.startGeolocationWatch(onLocationUpdate)
      }
    }

    document.addEventListener('visibilitychange', this.visibilityChangeHandler)
  }

  stopTracking(): void {
    console.log('Stopping all location tracking')

    // Clear geolocation watch
    if (this.watchId !== null) {
      navigator.geolocation.clearWatch(this.watchId)
      this.watchId = null
    }

    // Clear background interval
    if (this.backgroundInterval) {
      clearInterval(this.backgroundInterval)
      this.backgroundInterval = null
    }

    // Remove visibility change handler
    if (this.visibilityChangeHandler) {
      document.removeEventListener('visibilitychange', this.visibilityChangeHandler)
      this.visibilityChangeHandler = null
    }

    this.isTracking = false
  }

  private async saveLocation(locationData: LocationData): Promise<void> {
    const { error } = await supabase
      .from('locations')
      .upsert({
        device_id: this.deviceId,
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        accuracy: locationData.accuracy,
        timestamp: locationData.timestamp.toISOString()
      }, {
        onConflict: 'device_id'
      })

    if (error) {
      throw error
    }
  }

  async getLatestLocation(deviceId?: string): Promise<Location | null> {
    const targetDeviceId = deviceId || this.deviceId
    
    const { data, error } = await supabase
      .from('locations')
      .select('*')
      .eq('device_id', targetDeviceId)
      .order('timestamp', { ascending: false })
      .limit(1)
      .single()

    if (error && error.code !== 'PGRST116') {
      throw error
    }

    return data || null
  }

  subscribeToLocationUpdates(
    deviceId: string,
    onUpdate: (location: Location) => void,
    onError?: (error: any) => void
  ) {
    return supabase
      .channel(`location-updates-${deviceId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'locations',
          filter: `device_id=eq.${deviceId}`
        },
        (payload) => {
          if (payload.new) {
            onUpdate(payload.new as Location)
          }
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('Subscribed to location updates')
        } else if (status === 'CHANNEL_ERROR') {
          console.error('Error subscribing to location updates')
          onError?.(new Error('Subscription failed'))
        }
      })
  }

  getDeviceId(): string {
    return this.deviceId
  }

  isCurrentlyTracking(): boolean {
    return this.isTracking
  }
}
