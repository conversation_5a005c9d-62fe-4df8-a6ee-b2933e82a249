import { supabase, Location } from './supabase'
import { v4 as uuidv4 } from 'uuid'

export interface LocationData {
  latitude: number
  longitude: number
  accuracy: number
  timestamp: Date
}

export class LocationService {
  private deviceId: string
  private watchId: number | null = null
  private isTracking = false

  constructor(deviceId?: string) {
    this.deviceId = deviceId || this.generateDeviceId()
  }

  private generateDeviceId(): string {
    let deviceId = localStorage.getItem('device_id')
    if (!deviceId) {
      deviceId = uuidv4()
      localStorage.setItem('device_id', deviceId)
    }
    return deviceId
  }

  async startTracking(onLocationUpdate?: (location: LocationData) => void): Promise<void> {
    if (this.isTracking) {
      console.log('Already tracking location')
      return
    }

    if (!navigator.geolocation) {
      throw new Error('Geolocation is not supported by this browser')
    }

    return new Promise((resolve, reject) => {
      const options: PositionOptions = {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 1000
      }

      this.watchId = navigator.geolocation.watchPosition(
        async (position) => {
          const locationData: LocationData = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: new Date()
          }

          try {
            await this.saveLocation(locationData)
            onLocationUpdate?.(locationData)
            
            if (!this.isTracking) {
              this.isTracking = true
              resolve()
            }
          } catch (error) {
            console.error('Error saving location:', error)
          }
        },
        (error) => {
          console.error('Geolocation error:', error)
          if (!this.isTracking) {
            reject(error)
          }
        },
        options
      )
    })
  }

  stopTracking(): void {
    if (this.watchId !== null) {
      navigator.geolocation.clearWatch(this.watchId)
      this.watchId = null
    }
    this.isTracking = false
  }

  private async saveLocation(locationData: LocationData): Promise<void> {
    const { error } = await supabase
      .from('locations')
      .upsert({
        device_id: this.deviceId,
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        accuracy: locationData.accuracy,
        timestamp: locationData.timestamp.toISOString()
      }, {
        onConflict: 'device_id'
      })

    if (error) {
      throw error
    }
  }

  async getLatestLocation(deviceId?: string): Promise<Location | null> {
    const targetDeviceId = deviceId || this.deviceId
    
    const { data, error } = await supabase
      .from('locations')
      .select('*')
      .eq('device_id', targetDeviceId)
      .order('timestamp', { ascending: false })
      .limit(1)
      .single()

    if (error && error.code !== 'PGRST116') {
      throw error
    }

    return data || null
  }

  subscribeToLocationUpdates(
    deviceId: string,
    onUpdate: (location: Location) => void,
    onError?: (error: any) => void
  ) {
    return supabase
      .channel(`location-updates-${deviceId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'locations',
          filter: `device_id=eq.${deviceId}`
        },
        (payload) => {
          if (payload.new) {
            onUpdate(payload.new as Location)
          }
        }
      )
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log('Subscribed to location updates')
        } else if (status === 'CHANNEL_ERROR') {
          console.error('Error subscribing to location updates')
          onError?.(new Error('Subscription failed'))
        }
      })
  }

  getDeviceId(): string {
    return this.deviceId
  }

  isCurrentlyTracking(): boolean {
    return this.isTracking
  }
}
